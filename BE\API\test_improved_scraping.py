#!/usr/bin/env python3
"""
Test Improved Scraping API

This script tests the improved scraping API with:
1. Fixed database title issue
2. Duplicate key error handling
3. Pagination support for comprehensive chapter collection
"""

import requests
import json
import time
from datetime import datetime

def test_comprehensive_story_scraping():
    """Test comprehensive story scraping with pagination"""
    print("🔍 Testing Comprehensive Story Scraping with Pagination")
    print("="*70)
    
    test_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    payload = {
        "story_url": test_url,
        "include_chapters": True,
        "max_chapters": 10  # Limit response but collect all URLs
    }
    
    try:
        print(f"📖 Testing comprehensive story scraping: {test_url}")
        
        response = requests.post(
            "http://localhost:8000/api/v1/scraping/story-info",
            json=payload,
            timeout=300  # Longer timeout for pagination
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            if data.get("success"):
                story_id = data.get("story_id")
                title = data.get("title")
                author = data.get("author")
                description = data.get("description")
                cover_image = data.get("cover_image_url")
                total_chapters = data.get("total_chapters", 0)
                chapters = data.get("chapters", [])
                
                print(f"✅ Story scraped successfully!")
                print(f"   Story ID: {story_id}")
                print(f"   Title: {title}")
                print(f"   Author: {author}")
                print(f"   Description: {description[:100] if description else 'None'}...")
                print(f"   Cover Image: {cover_image}")
                print(f"   Total chapters found: {total_chapters}")
                print(f"   Chapters in response: {len(chapters)}")
                
                # Show first few chapters
                print(f"\n📄 Chapter samples:")
                for i, chapter in enumerate(chapters[:5]):
                    print(f"   Chapter {i+1}: {chapter.get('title', 'No title')}")
                    print(f"     URL: {chapter.get('url', 'No URL')}")
                
                # Test database persistence with proper title
                print(f"\n📊 Testing database persistence...")
                return test_database_title_fix(story_id, title)
                
            else:
                print(f"❌ Story scraping failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_database_title_fix(story_id, expected_title):
    """Test that database title is properly saved"""
    try:
        # Get story from database
        response = requests.get(f"http://localhost:8000/api/v1/data/stories/{story_id}", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                story_data = data.get("data", {})
                db_title = story_data.get("title")
                db_author = story_data.get("author")
                db_description = story_data.get("description")
                db_cover_image = story_data.get("cover_image_url")
                db_total_chapters = story_data.get("total_chapters", 0)
                
                print(f"✅ Story found in database")
                print(f"   Database title: {db_title}")
                print(f"   Database author: {db_author}")
                print(f"   Database description: {db_description[:100] if db_description else 'None'}...")
                print(f"   Database cover image: {db_cover_image}")
                print(f"   Database total chapters: {db_total_chapters}")
                
                # Check title fix
                if db_title and db_title != "None" and db_title.strip():
                    print(f"✅ Title fix successful: '{db_title}'")
                    title_fixed = True
                else:
                    print(f"❌ Title still None or empty: '{db_title}'")
                    title_fixed = False
                
                # Check if Vietnamese characters are preserved
                if db_title and any(ord(char) > 127 for char in db_title):
                    print(f"✅ Vietnamese characters preserved in database")
                
                # Check total chapters
                if db_total_chapters > 0:
                    print(f"✅ Total chapters properly saved: {db_total_chapters}")
                else:
                    print(f"⚠️  Total chapters not saved properly: {db_total_chapters}")
                
                return title_fixed
            else:
                print(f"❌ Database retrieval failed: {data.get('message')}")
                return False
        else:
            print(f"❌ Database request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Database retrieval test failed: {e}")
        return False

def test_duplicate_key_handling():
    """Test duplicate key error handling by running the same request twice"""
    print(f"\n🔍 Testing Duplicate Key Error Handling")
    print("="*70)
    
    test_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    payload = {
        "story_url": test_url,
        "include_chapters": True,
        "max_chapters": 3  # Small number for quick test
    }
    
    results = []
    
    for attempt in range(2):
        print(f"\n📖 Attempt {attempt + 1}: Testing duplicate handling")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/scraping/story-info",
                json=payload,
                timeout=120
            )
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    title = data.get("title")
                    total_chapters = data.get("total_chapters", 0)
                    print(f"   ✅ Success: {title} ({total_chapters} chapters)")
                    results.append(True)
                else:
                    print(f"   ❌ Failed: {data.get('message')}")
                    results.append(False)
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            results.append(False)
        
        # Small delay between attempts
        if attempt < 1:
            time.sleep(2)
    
    success_count = sum(results)
    print(f"\n📊 Duplicate handling test: {success_count}/2 attempts successful")
    
    # Both should succeed if duplicate handling works
    return success_count == 2

def test_pagination_metadata():
    """Test that pagination metadata is properly included"""
    print(f"\n🔍 Testing Pagination Metadata")
    print("="*70)
    
    test_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    payload = {
        "story_url": test_url,
        "include_chapters": True,
        "max_chapters": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/scraping/story-info",
            json=payload,
            timeout=180
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                story_id = data.get("story_id")
                total_chapters = data.get("total_chapters", 0)
                chapters_in_response = len(data.get("chapters", []))
                
                print(f"✅ Story scraped successfully")
                print(f"   Total chapters found: {total_chapters}")
                print(f"   Chapters in response: {chapters_in_response}")
                
                # Check database for pagination metadata
                db_response = requests.get(f"http://localhost:8000/api/v1/data/stories/{story_id}")
                if db_response.status_code == 200:
                    db_data = db_response.json()
                    if db_data.get("success"):
                        story_data = db_data.get("data", {})
                        metadata = story_data.get("metadata", {})
                        
                        pages_crawled = metadata.get("total_pages_crawled", 0)
                        all_chapter_urls = metadata.get("all_chapter_urls", [])
                        
                        print(f"   Pages crawled: {pages_crawled}")
                        print(f"   All chapter URLs collected: {len(all_chapter_urls)}")
                        
                        # Verify pagination worked
                        if pages_crawled > 1:
                            print(f"✅ Pagination worked: {pages_crawled} pages crawled")
                        else:
                            print(f"⚠️  Only 1 page crawled (might be expected)")
                        
                        if len(all_chapter_urls) >= total_chapters:
                            print(f"✅ All chapter URLs collected: {len(all_chapter_urls)}")
                        else:
                            print(f"⚠️  Not all URLs collected: {len(all_chapter_urls)}/{total_chapters}")
                        
                        return True
                
                return False
            else:
                print(f"❌ Scraping failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run all improvement tests"""
    print("🚀 Testing Improved Scraping API")
    print("="*70)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not healthy")
            return False
        print("✅ Server is healthy")
    except:
        print("❌ Cannot connect to server")
        return False
    
    # Run tests
    tests = [
        ("Comprehensive Story Scraping", test_comprehensive_story_scraping),
        ("Duplicate Key Handling", test_duplicate_key_handling),
        ("Pagination Metadata", test_pagination_metadata)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 FINAL TEST SUMMARY")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"Test completed at: {datetime.now().isoformat()}")
    
    if passed == total:
        print("\n🎉 ALL IMPROVEMENTS WORKING!")
        print("✅ Database title issue fixed")
        print("✅ Duplicate key errors handled gracefully")
        print("✅ Pagination support implemented")
        print("✅ Comprehensive chapter URL collection working")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
