#!/usr/bin/env python3
"""
Clean Database Script

This script cleans up corrupted database entries to allow fresh testing.
"""

import asyncio
import pymongo
from datetime import datetime

async def clean_database():
    """Clean up corrupted database entries"""
    print("🧹 Cleaning Database")
    print("="*50)
    
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017")
        db = client["webtruyen_api"]
        
        # Clean chapters with null story_id
        chapters_collection = db["chapters"]
        result = chapters_collection.delete_many({"story_id": None})
        print(f"✅ Deleted {result.deleted_count} chapters with null story_id")
        
        # Clean orphaned chapters (chapters without valid story)
        stories_collection = db["stories"]
        story_ids = [str(story["_id"]) for story in stories_collection.find({}, {"_id": 1})]
        
        orphaned_result = chapters_collection.delete_many({"story_id": {"$nin": story_ids}})
        print(f"✅ Deleted {orphaned_result.deleted_count} orphaned chapters")
        
        # Clean old scraping jobs
        jobs_collection = db["scraping_jobs"]
        old_jobs_result = jobs_collection.delete_many({
            "created_at": {"$lt": datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)}
        })
        print(f"✅ Deleted {old_jobs_result.deleted_count} old scraping jobs")
        
        # Show current database state
        stories_count = stories_collection.count_documents({})
        chapters_count = chapters_collection.count_documents({})
        jobs_count = jobs_collection.count_documents({})
        
        print(f"\n📊 Current Database State:")
        print(f"   Stories: {stories_count}")
        print(f"   Chapters: {chapters_count}")
        print(f"   Jobs: {jobs_count}")
        
        # Show sample story
        sample_story = stories_collection.find_one({})
        if sample_story:
            print(f"\n📖 Sample Story:")
            print(f"   ID: {sample_story['_id']}")
            print(f"   Title: {sample_story.get('title', 'No title')}")
            print(f"   URL: {sample_story.get('url', 'No URL')}")
        
        client.close()
        print("\n✅ Database cleanup completed!")
        return True
        
    except Exception as e:
        print(f"❌ Database cleanup failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(clean_database())
    exit(0 if success else 1)
