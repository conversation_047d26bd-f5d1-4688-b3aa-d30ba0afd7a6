2025-07-09 13:53:53 | ERROR    | src.scraper_engine:scrape_url:199 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-phan-thu-ky-cuu-cau-nguyet-phieu-1/: Page.goto: net::ERR_ABORTED at https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-phan-thu-ky-cuu-cau-nguyet-phieu-1/
Call log:
  - navigating to "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-phan-thu-ky-cuu-cau-nguyet-phieu-1/", waiting until "networkidle"


2025-07-09 13:53:54 | ERROR    | src.scraper_engine:scrape_url:199 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-chien-luc-phan-dinh-cau-nguyet-phieu-2/: Page.goto: net::ERR_ABORTED at https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-chien-luc-phan-dinh-cau-nguyet-phieu-2/
Call log:
  - navigating to "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-chien-luc-phan-dinh-cau-nguyet-phieu-2/", waiting until "networkidle"


2025-07-09 13:54:20 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-ph...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}

Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-ph...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}
2025-07-09 13:54:20 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-phan-thu-ky-cuu-cau-nguyet-phieu-2/: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-ph...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 358, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}

Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1F571EB0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F4816C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F5EF930>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566, 'title': 'Chương 565: Uẩn Dưỡng Chữa Thương, Chiến Lực Phán Đ...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-ph...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 358, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 566 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 566}}
2025-07-09 13:54:34 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-ch...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}

Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-ch...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}
2025-07-09 13:54:34 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-chien-luc-phan-dinh-cau-nguyet-phieu-1/: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-ch...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 358, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}

Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
                   │    └ <property object at 0x0000020D74761580>
                   └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x0000020D7456E520>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x0000020D74586070>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x0000020D745D2C00>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x0000020D745D2D40>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x0000020D745D2CA0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x0000020D745D4360>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function _ClientConnectionRetryable._read at 0x0000020D745D4400>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x0000020D1ECEB820>) at 2255369974352
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x0000020D1E7C7800>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x0000020D1F734FE0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x0000020D1F733310>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x0000020D73AA53A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x0000020D73AA51C0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 354, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565, 'title': 'Chương 564: Kinh Lôi Một Quyền, Võ Đạo Chi Tâm (Cầu...
          │               └ <function DatabaseService.insert_one at 0x0000020D747CB2E0>
          └ <utils.database.ChapterService object at 0x0000020D7462F620>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x0000020D71EE2AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000020D72037770>
         │    └ <function Command.invoke at 0x0000020D71EE27A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x0000020D72037770>
           │   │      │    └ <function main at 0x0000020D721127A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x0000020D71EE19E0>
           └ <click.core.Context object at 0x0000020D72037770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x0000020D721127A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x0000020D71F74A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000020D71F9FA60>
    └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000020D71F9FB00>
           │       │   └ <uvicorn.server.Server object at 0x0000020D7211C1A0>
           │       └ <function run at 0x0000020D6F0F65C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020D720C3BC0>
           │      └ <function Runner.run at 0x0000020D7148F380>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020D7148CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020D7211C440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020D7148CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020D7148ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020D712CA200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e11fd4bebc031fdb32c61'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-ch...
          │    └ <function ScrapingController._save_chapter_data at 0x0000020D75C5DF80>
          └ <routers.scraping.ScrapingController object at 0x0000020D75C8C6E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 358, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: "686de8c8278cd2981f0e224a", chapter_number: 565 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': '686de8c8278cd2981f0e224a', 'chapter_number': 565}}
2025-07-09 14:16:22 | ERROR    | src.scraper_engine:scrape_url:199 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-563-linh-tinh-ton-thuong-chien-dau-kiem-ke-cau-nguyet-phieu-3/: Page.goto: net::ERR_ABORTED at https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-563-linh-tinh-ton-thuong-chien-dau-kiem-ke-cau-nguyet-phieu-3/
Call log:
  - navigating to "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-563-linh-tinh-ton-thuong-chien-dau-kiem-ke-cau-nguyet-phieu-3/", waiting until "networkidle"


2025-07-09 14:16:22 | ERROR    | src.scraper_engine:scrape_url:199 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-dao-chi-tam-cau-nguyet-phieu/: Page.goto: net::ERR_ABORTED at https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-dao-chi-tam-cau-nguyet-phieu/
Call log:
  - navigating to "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-dao-chi-tam-cau-nguyet-phieu/", waiting until "networkidle"


2025-07-09 14:16:45 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: 'NoneType' object has no attribute 'split'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A461692AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A4617FF770>
         │    └ <function Command.invoke at 0x000001A4616927A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x000001A4617FF770>
           │   │      │    └ <function main at 0x000001A4618E27A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x000001A4616919E0>
           └ <click.core.Context object at 0x000001A4617FF770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x000001A4618E27A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x000001A461724A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A46176FA60>
    └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A46176FB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
           │       └ <function run at 0x000001A45E9665C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A461893BC0>
           │      └ <function Runner.run at 0x000001A460C2F380>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A460C2CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001A460C2CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A460C2ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A460B3A200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 265, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e172c493e3c00ad8e704f'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...
          │    └ <function ScrapingController._save_chapter_data at 0x000001A465489DA0>
          └ <routers.scraping.ScrapingController object at 0x000001A4654B06E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    "word_count": len(content_data["content"].split()),
                      └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...

AttributeError: 'NoneType' object has no attribute 'split'

Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A461692AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A4617FF770>
         │    └ <function Command.invoke at 0x000001A4616927A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x000001A4617FF770>
           │   │      │    └ <function main at 0x000001A4618E27A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x000001A4616919E0>
           └ <click.core.Context object at 0x000001A4617FF770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x000001A4618E27A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x000001A461724A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A46176FA60>
    └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A46176FB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
           │       └ <function run at 0x000001A45E9665C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A461893BC0>
           │      └ <function Runner.run at 0x000001A460C2F380>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A460C2CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001A460C2CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A460C2ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A460B3A200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 265, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e172c493e3c00ad8e704f'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...
          │    └ <function ScrapingController._save_chapter_data at 0x000001A465489DA0>
          └ <routers.scraping.ScrapingController object at 0x000001A4654B06E0>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    "word_count": len(content_data["content"].split()),
                      └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...

AttributeError: 'NoneType' object has no attribute 'split'
2025-07-09 14:16:45 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-dao-chi-tam-cau-nguyet-phieu/: Failed to save chapter data: 'NoneType' object has no attribute 'split'
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    "word_count": len(content_data["content"].split()),
                      └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...

AttributeError: 'NoneType' object has no attribute 'split'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A461692AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A4617FF770>
         │    └ <function Command.invoke at 0x000001A4616927A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x000001A4617FF770>
           │   │      │    └ <function main at 0x000001A4618E27A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x000001A4616919E0>
           └ <click.core.Context object at 0x000001A4617FF770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x000001A4618E27A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x000001A461724A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A46176FA60>
    └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A46176FB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
           │       └ <function run at 0x000001A45E9665C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A461893BC0>
           │      └ <function Runner.run at 0x000001A460C2F380>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A460C2CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001A460C2CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A460C2ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A460B3A200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 265, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e172c493e3c00ad8e704f'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...
          │    └ <function ScrapingController._save_chapter_data at 0x000001A465489DA0>
          └ <routers.scraping.ScrapingController object at 0x000001A4654B06E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 401, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: 'NoneType' object has no attribute 'split'

Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    "word_count": len(content_data["content"].split()),
                      └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...

AttributeError: 'NoneType' object has no attribute 'split'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\__main__.py", line 4, in <module>
    uvicorn.main()
    │       └ <Command main>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\uvicorn\\__init__.py'>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A461692AC0>
           └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A4617FF770>
         │    └ <function Command.invoke at 0x000001A4616927A0>
         └ <Command main>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │   │      │    │           └ <click.core.Context object at 0x000001A4617FF770>
           │   │      │    └ <function main at 0x000001A4618E27A0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x000001A4616919E0>
           └ <click.core.Context object at 0x000001A4617FF770>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 8000, 'app': 'main:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'reload_inc...
           │         └ ()
           └ <function main at 0x000001A4618E27A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 413, in main
    run(
    └ <function run at 0x000001A461724A40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A46176FA60>
    └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A46176FB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A4618EC1A0>
           │       └ <function run at 0x000001A45E9665C0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A461893BC0>
           │      └ <function Runner.run at 0x000001A460C2F380>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A460C2CF40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A4618EC440>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001A460C2CEA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A460C2ECA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A460B3A200>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 265, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686e172c493e3c00ad8e704f'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-da...
          │    └ <function ScrapingController._save_chapter_data at 0x000001A465489DA0>
          └ <routers.scraping.ScrapingController object at 0x000001A4654B06E0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 401, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: 'NoneType' object has no attribute 'split'
2025-07-09 14:16:49 | ERROR    | src.scraper_engine:scrape_url:199 | Failed to scrape https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-562-van-ma-chan-than-thanh-nu-chi-nhuc-cau-nguyet-phieu-4/: Page.goto: net::ERR_ABORTED at https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-562-van-ma-chan-than-thanh-nu-chi-nhuc-cau-nguyet-phieu-4/
Call log:
  - navigating to "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-562-van-ma-chan-than-thanh-nu-chi-nhuc-cau-nguyet-phieu-4/", waiting until "networkidle"


