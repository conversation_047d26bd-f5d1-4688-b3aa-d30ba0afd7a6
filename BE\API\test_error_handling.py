#!/usr/bin/env python3
"""
Test Error <PERSON>ling and Edge Cases

This script tests various error scenarios and edge cases to ensure
the web scraping API handles them gracefully.
"""

import requests
import json
import time

def test_invalid_urls():
    """Test handling of invalid URLs"""
    print("🔍 Testing Invalid URL Handling...")
    
    invalid_urls = [
        "https://invalid-domain-that-does-not-exist.com/story",
        "https://webtruyen.diendantruyen.com/invalid-story-path",
        "not-a-valid-url-at-all",
        "https://google.com",  # Valid URL but not a story
        ""  # Empty URL
    ]
    
    results = []
    
    for i, url in enumerate(invalid_urls):
        print(f"   Testing invalid URL {i+1}: {url[:50]}...")
        
        try:
            payload = {
                "story_url": url,
                "include_chapters": False
            }
            
            response = requests.post(
                "http://localhost:8000/api/v1/scraping/story-info", 
                json=payload, 
                timeout=30
            )
            
            if response.status_code in [400, 404, 422, 500]:
                data = response.json()
                if not data.get("success", True):  # Should be False for errors
                    results.append(True)
                    print(f"     ✅ Properly handled with status {response.status_code}")
                else:
                    results.append(False)
                    print(f"     ❌ Error not properly indicated in response")
            else:
                results.append(False)
                print(f"     ❌ Unexpected status: {response.status_code}")
                
        except requests.exceptions.Timeout:
            results.append(True)
            print(f"     ✅ Timeout handled gracefully")
        except Exception as e:
            results.append(False)
            print(f"     ❌ Unexpected exception: {e}")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ Invalid URL handling: {passed}/{total} tests passed")
        return True
    else:
        print(f"❌ Invalid URL handling: {passed}/{total} tests passed")
        return False

def test_malformed_requests():
    """Test handling of malformed requests"""
    print("\n🔍 Testing Malformed Request Handling...")
    
    malformed_requests = [
        {},  # Empty request
        {"invalid_field": "value"},  # Wrong field names
        {"story_url": 123},  # Wrong data type
        {"story_url": "valid-url", "include_chapters": "not-boolean"},  # Wrong type
        {"story_url": "valid-url", "max_chapters": "not-number"},  # Wrong type
        None  # Null request
    ]
    
    results = []
    
    for i, payload in enumerate(malformed_requests):
        print(f"   Testing malformed request {i+1}...")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/scraping/story-info", 
                json=payload, 
                timeout=10
            )
            
            if response.status_code == 422:  # Validation error
                results.append(True)
                print(f"     ✅ Validation error properly handled")
            elif response.status_code in [400, 500]:
                data = response.json()
                if not data.get("success", True):
                    results.append(True)
                    print(f"     ✅ Error properly handled with status {response.status_code}")
                else:
                    results.append(False)
                    print(f"     ❌ Error not properly indicated")
            else:
                results.append(False)
                print(f"     ❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            results.append(False)
            print(f"     ❌ Unexpected exception: {e}")
    
    passed = sum(results)
    total = len(results)
    
    if passed >= total * 0.8:  # Allow some flexibility
        print(f"✅ Malformed request handling: {passed}/{total} tests passed")
        return True
    else:
        print(f"❌ Malformed request handling: {passed}/{total} tests passed")
        return False

def test_network_timeouts():
    """Test handling of network timeouts and slow responses"""
    print("\n🔍 Testing Network Timeout Handling...")
    
    try:
        # Test with a very short timeout
        payload = {
            "story_url": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/",
            "include_chapters": False
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/scraping/story-info", 
            json=payload, 
            timeout=1  # Very short timeout
        )
        
        # If we get here, the request was fast enough
        if response.status_code == 200:
            print("     ✅ Request completed within timeout")
            return True
        else:
            print(f"     ⚠️  Request failed with status {response.status_code}")
            return True  # Still handled gracefully
            
    except requests.exceptions.Timeout:
        print("     ✅ Timeout handled gracefully by client")
        return True
    except Exception as e:
        print(f"     ❌ Unexpected exception: {e}")
        return False

def test_large_concurrent_load():
    """Test system behavior under high concurrent load"""
    print("\n🔍 Testing High Concurrent Load...")
    
    import threading
    import time
    
    results = []
    errors = []
    start_time = time.time()
    
    def make_concurrent_request(request_id):
        try:
            response = requests.get(
                "http://localhost:8000/api/v1/data/stories", 
                timeout=30
            )
            
            end_time = time.time()
            results.append({
                "id": request_id,
                "status": response.status_code,
                "time": end_time - start_time,
                "success": response.status_code == 200
            })
            
        except Exception as e:
            errors.append({
                "id": request_id,
                "error": str(e),
                "time": time.time() - start_time
            })
    
    # Launch 20 concurrent requests
    threads = []
    for i in range(20):
        thread = threading.Thread(target=make_concurrent_request, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    total_requests = len(results) + len(errors)
    successful = sum(1 for r in results if r.get("success", False))
    failed = len(errors)
    
    print(f"   Total requests: {total_requests}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {failed}")
    
    # Consider it successful if at least 80% of requests succeeded
    success_rate = successful / total_requests if total_requests > 0 else 0
    
    if success_rate >= 0.8:
        print(f"✅ High load handling: {success_rate:.1%} success rate")
        return True
    else:
        print(f"❌ High load handling: {success_rate:.1%} success rate (below 80%)")
        return False

def test_database_error_scenarios():
    """Test database-related error scenarios"""
    print("\n🔍 Testing Database Error Scenarios...")
    
    try:
        # Test database health endpoint
        response = requests.get("http://localhost:8000/health/database", timeout=10)
        
        if response.status_code == 200:
            print("     ✅ Database health check passed")
            
            # Test data retrieval with potential edge cases
            test_cases = [
                "/api/v1/data/stories?page=999999",  # Very high page number
                "/api/v1/data/stories?page_size=1000",  # Large page size
                "/api/v1/data/stories?page=0",  # Invalid page number
                "/api/v1/data/stories?page_size=0",  # Invalid page size
            ]
            
            results = []
            for endpoint in test_cases:
                try:
                    response = requests.get(f"http://localhost:8000{endpoint}", timeout=10)
                    if response.status_code in [200, 400, 422]:  # Expected responses
                        results.append(True)
                        print(f"     ✅ {endpoint} handled properly")
                    else:
                        results.append(False)
                        print(f"     ❌ {endpoint} unexpected status: {response.status_code}")
                except Exception as e:
                    results.append(False)
                    print(f"     ❌ {endpoint} exception: {e}")
            
            passed = sum(results)
            total = len(results)
            
            if passed >= total * 0.75:  # Allow some flexibility
                print(f"✅ Database error scenarios: {passed}/{total} handled properly")
                return True
            else:
                print(f"❌ Database error scenarios: {passed}/{total} handled properly")
                return False
                
        else:
            print(f"     ❌ Database health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"     ❌ Database error testing failed: {e}")
        return False

def test_api_rate_limiting():
    """Test API rate limiting behavior"""
    print("\n🔍 Testing API Rate Limiting...")
    
    # Make rapid requests to trigger rate limiting
    responses = []
    start_time = time.time()
    
    for i in range(50):  # Make 50 rapid requests
        try:
            response = requests.get("http://localhost:8000/api/v1/data/stories", timeout=5)
            responses.append({
                "status": response.status_code,
                "time": time.time() - start_time,
                "headers": dict(response.headers)
            })
        except Exception as e:
            responses.append({
                "status": "error",
                "time": time.time() - start_time,
                "error": str(e)
            })
    
    # Analyze responses
    rate_limited = sum(1 for r in responses if r.get("status") == 429)
    successful = sum(1 for r in responses if r.get("status") == 200)
    errors = sum(1 for r in responses if r.get("status") == "error")
    
    print(f"   Total requests: {len(responses)}")
    print(f"   Successful (200): {successful}")
    print(f"   Rate limited (429): {rate_limited}")
    print(f"   Errors: {errors}")
    
    # Check for rate limiting headers
    has_rate_limit_headers = any(
        "X-RateLimit" in str(r.get("headers", {})) or 
        "RateLimit" in str(r.get("headers", {}))
        for r in responses if isinstance(r.get("headers"), dict)
    )
    
    if rate_limited > 0:
        print("✅ Rate limiting is active")
        return True
    elif has_rate_limit_headers:
        print("✅ Rate limiting headers present (soft limiting)")
        return True
    else:
        print("⚠️  No rate limiting detected")
        return True  # Not necessarily a failure

def main():
    print("🚀 Testing Error Handling and Edge Cases")
    print("="*60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running or not healthy")
            return False
        print("✅ Server is running and healthy")
    except:
        print("❌ Cannot connect to server. Make sure it's running on localhost:8000")
        return False
    
    # Run error handling tests
    tests = [
        ("Invalid URLs", test_invalid_urls),
        ("Malformed Requests", test_malformed_requests),
        ("Network Timeouts", test_network_timeouts),
        ("High Concurrent Load", test_large_concurrent_load),
        ("Database Error Scenarios", test_database_error_scenarios),
        ("API Rate Limiting", test_api_rate_limiting)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 ERROR HANDLING TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed >= total * 0.8:  # Allow some flexibility for edge cases
        print("🎉 Error handling tests mostly passed!")
        print("✅ The system handles errors and edge cases reasonably well")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
