"""
Search and Filter API Endpoints

This module provides advanced search and filtering capabilities
for stories and chapters with content comparison features.
"""

from typing import List, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from models.api_models import (
    SearchRequest, StoryListResponse, StoryListItem,
    ContentComparisonRequest, ContentComparisonResponse,
    PaginationInfo, APIResponse
)
from models.database import StoryStatus, EnhancementStatus
from utils.database import story_service, chapter_service
from middleware.error_handling import DatabaseError
from utils.logging_config import LoggerMixin

router = APIRouter()


class SearchController(LoggerMixin):
    """Controller for search and filtering operations"""
    
    async def advanced_search(self, request: SearchRequest) -> StoryListResponse:
        """Perform advanced search across stories and content"""
        try:
            self.log_info(f"Performing advanced search: {request.query}")
            
            # Build search pipeline
            search_pipeline = []
            
            # Text search stage
            if request.query:
                search_conditions = []
                
                # Search in different fields based on search_in parameter
                for field in request.search_in:
                    if field == "title":
                        search_conditions.append({
                            "title": {"$regex": request.query, "$options": "i"}
                        })
                    elif field == "author":
                        search_conditions.append({
                            "metadata.author": {"$regex": request.query, "$options": "i"}
                        })
                    elif field == "description":
                        search_conditions.append({
                            "metadata.description": {"$regex": request.query, "$options": "i"}
                        })
                    elif field == "content":
                        # This would require a more complex aggregation with chapters
                        # For now, we'll skip content search in this implementation
                        pass
                
                if search_conditions:
                    search_pipeline.append({
                        "$match": {"$or": search_conditions}
                    })
            
            # Apply additional filters
            filter_conditions = {}
            
            for key, value in request.filters.items():
                if key == "status" and value:
                    filter_conditions["metadata.status"] = value
                elif key == "author" and value:
                    filter_conditions["metadata.author"] = {"$regex": value, "$options": "i"}
                elif key == "genre" and value:
                    filter_conditions["metadata.genres"] = value
                elif key == "scraping_status" and value:
                    filter_conditions["scraping_status"] = value
                elif key == "min_chapters" and value:
                    filter_conditions["total_chapters_scraped"] = {"$gte": int(value)}
                elif key == "max_chapters" and value:
                    filter_conditions.setdefault("total_chapters_scraped", {})["$lte"] = int(value)
                elif key == "enhanced_only" and value:
                    filter_conditions["total_chapters_enhanced"] = {"$gt": 0}
            
            if filter_conditions:
                search_pipeline.append({"$match": filter_conditions})
            
            # Add sorting
            sort_direction = -1 if request.sort_order == "desc" else 1
            search_pipeline.append({
                "$sort": {request.sort_by: sort_direction}
            })
            
            # Add pagination
            search_pipeline.extend([
                {"$skip": (request.page - 1) * request.page_size},
                {"$limit": request.page_size}
            ])
            
            # Execute search
            if search_pipeline:
                stories = await story_service.aggregate(search_pipeline)
            else:
                # Fallback to simple find if no pipeline
                stories, _ = await story_service.search_stories(
                    query=request.query,
                    page=request.page,
                    page_size=request.page_size
                )
            
            # Get total count for pagination (separate query)
            count_pipeline = search_pipeline[:-2]  # Remove skip and limit
            count_pipeline.append({"$count": "total"})
            
            if count_pipeline:
                count_result = await story_service.aggregate(count_pipeline)
                total_count = count_result[0]["total"] if count_result else 0
            else:
                total_count = len(stories)
            
            # Convert to response format
            story_items = []
            for story in stories:
                item = StoryListItem(
                    id=str(story["_id"]),
                    title=story["title"],
                    author=story.get("metadata", {}).get("author"),
                    status=StoryStatus(story.get("metadata", {}).get("status", "unknown")),
                    total_chapters_scraped=story.get("total_chapters_scraped", 0),
                    total_chapters_enhanced=story.get("total_chapters_enhanced", 0),
                    enhancement_progress=story.get("enhancement_progress", 0.0),
                    cover_image_url=story.get("metadata", {}).get("cover_image_url"),
                    created_at=story["created_at"],
                    updated_at=story["updated_at"]
                )
                story_items.append(item)
            
            # Calculate pagination info
            total_pages = (total_count + request.page_size - 1) // request.page_size
            pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total_items=total_count,
                total_pages=total_pages,
                has_next=request.page < total_pages,
                has_previous=request.page > 1
            )
            
            return StoryListResponse(
                success=True,
                message=f"Found {len(story_items)} stories matching search criteria",
                pagination=pagination,
                data=story_items
            )
            
        except Exception as e:
            self.log_error(f"Error in advanced search: {e}")
            raise DatabaseError(f"Search failed: {e}")
    
    async def compare_content(self, request: ContentComparisonRequest) -> ContentComparisonResponse:
        """Compare original and enhanced content for a chapter"""
        try:
            self.log_info(f"Comparing content for chapter: {request.chapter_id}")
            
            # Get chapter
            chapter = await chapter_service.find_by_id(request.chapter_id)
            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found")
            
            original_content = chapter.get("original_content", "")
            enhanced_content = chapter.get("enhanced_content", "")
            
            if not enhanced_content:
                return ContentComparisonResponse(
                    success=False,
                    message="Chapter has not been enhanced yet",
                    chapter_id=request.chapter_id,
                    chapter_title=chapter["title"],
                    original_length=len(original_content),
                    enhanced_length=0
                )
            
            # Calculate metrics
            original_length = len(original_content)
            enhanced_length = len(enhanced_content)
            
            # Get enhancement metadata
            enhancement_metadata = chapter.get("enhancement_metadata", {})
            similarity_score = enhancement_metadata.get("similarity_score")
            improvement_notes = enhancement_metadata.get("improvement_notes", [])
            
            # Generate comparison data based on type
            comparison_data = {}
            
            if request.comparison_type == "side_by_side":
                comparison_data = {
                    "original": original_content,
                    "enhanced": enhanced_content
                }
            
            elif request.comparison_type == "diff":
                comparison_data = self._generate_diff(original_content, enhanced_content)
            
            elif request.comparison_type == "statistics":
                comparison_data = self._generate_statistics(original_content, enhanced_content)
            
            return ContentComparisonResponse(
                success=True,
                message="Content comparison generated successfully",
                chapter_id=request.chapter_id,
                chapter_title=chapter["title"],
                original_length=original_length,
                enhanced_length=enhanced_length,
                similarity_score=similarity_score,
                improvement_notes=improvement_notes,
                comparison_data=comparison_data
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error comparing content: {e}")
            raise DatabaseError(f"Content comparison failed: {e}")
    
    async def get_search_suggestions(self, query: str, limit: int = 10) -> List[str]:
        """Get search suggestions based on partial query"""
        try:
            self.log_info(f"Getting search suggestions for: {query}")
            
            suggestions = []
            
            # Get title suggestions
            title_pipeline = [
                {
                    "$match": {
                        "title": {"$regex": f"^{query}", "$options": "i"}
                    }
                },
                {
                    "$project": {"title": 1}
                },
                {
                    "$limit": limit // 2
                }
            ]
            
            title_results = await story_service.aggregate(title_pipeline)
            suggestions.extend([story["title"] for story in title_results])
            
            # Get author suggestions
            author_pipeline = [
                {
                    "$match": {
                        "metadata.author": {"$regex": f"^{query}", "$options": "i"}
                    }
                },
                {
                    "$group": {"_id": "$metadata.author"}
                },
                {
                    "$limit": limit // 2
                }
            ]
            
            author_results = await story_service.aggregate(author_pipeline)
            suggestions.extend([result["_id"] for result in author_results if result["_id"]])
            
            # Remove duplicates and limit
            suggestions = list(set(suggestions))[:limit]
            
            return suggestions
            
        except Exception as e:
            self.log_error(f"Error getting search suggestions: {e}")
            return []
    
    def _generate_diff(self, original: str, enhanced: str) -> Dict[str, Any]:
        """Generate diff between original and enhanced content"""
        try:
            import difflib
            
            original_lines = original.splitlines()
            enhanced_lines = enhanced.splitlines()
            
            diff = list(difflib.unified_diff(
                original_lines,
                enhanced_lines,
                fromfile="original",
                tofile="enhanced",
                lineterm=""
            ))
            
            return {
                "diff_lines": diff,
                "changes_count": len([line for line in diff if line.startswith(('+', '-'))])
            }
            
        except Exception as e:
            self.log_error(f"Error generating diff: {e}")
            return {"error": "Failed to generate diff"}
    
    def _generate_statistics(self, original: str, enhanced: str) -> Dict[str, Any]:
        """Generate statistics comparison"""
        try:
            original_words = original.split()
            enhanced_words = enhanced.split()
            
            original_sentences = original.count('.')
            enhanced_sentences = enhanced.count('.')
            
            original_paragraphs = len([p for p in original.split('\n\n') if p.strip()])
            enhanced_paragraphs = len([p for p in enhanced.split('\n\n') if p.strip()])
            
            return {
                "word_count": {
                    "original": len(original_words),
                    "enhanced": len(enhanced_words),
                    "change": len(enhanced_words) - len(original_words)
                },
                "sentence_count": {
                    "original": original_sentences,
                    "enhanced": enhanced_sentences,
                    "change": enhanced_sentences - original_sentences
                },
                "paragraph_count": {
                    "original": original_paragraphs,
                    "enhanced": enhanced_paragraphs,
                    "change": enhanced_paragraphs - original_paragraphs
                },
                "character_count": {
                    "original": len(original),
                    "enhanced": len(enhanced),
                    "change": len(enhanced) - len(original)
                }
            }
            
        except Exception as e:
            self.log_error(f"Error generating statistics: {e}")
            return {"error": "Failed to generate statistics"}


# ============================================================================
# Router Endpoints
# ============================================================================

controller = SearchController()


@router.post("/advanced", response_model=StoryListResponse)
async def advanced_search(request: SearchRequest):
    """
    Perform advanced search with filters
    
    This endpoint provides advanced search capabilities with support for
    multiple search fields, filters, and sorting options.
    """
    return await controller.advanced_search(request)


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum suggestions")
):
    """
    Get search suggestions
    
    This endpoint returns search suggestions based on a partial query
    to help users with autocomplete functionality.
    """
    suggestions = await controller.get_search_suggestions(q, limit)
    
    return {
        "success": True,
        "message": f"Found {len(suggestions)} suggestions",
        "suggestions": suggestions,
        "timestamp": datetime.utcnow().isoformat()
    }


@router.post("/compare-content", response_model=ContentComparisonResponse)
async def compare_content(request: ContentComparisonRequest):
    """
    Compare original and enhanced content
    
    This endpoint compares the original and enhanced versions of a chapter
    and provides detailed comparison data including differences and statistics.
    """
    return await controller.compare_content(request)
