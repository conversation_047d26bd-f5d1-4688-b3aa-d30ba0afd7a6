"""
Logging Configuration Utilities

This module provides centralized logging configuration for the API application
with support for structured logging, file rotation, and different log levels.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from loguru import logger


def setup_logging(log_level: str = "INFO", log_file: str = "api.log"):
    """
    Setup comprehensive logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file
    """
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Remove default loguru handler
    logger.remove()
    
    # Console handler with colors
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # File handler with rotation
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )
    
    # Error file handler (errors only)
    error_file = log_path.parent / f"{log_path.stem}_errors.log"
    logger.add(
        str(error_file),
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}\n{exception}",
        rotation="5 MB",
        retention="14 days",
        compression="zip"
    )
    
    # Configure standard library logging to use loguru
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # Get corresponding Loguru level if it exists
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno
            
            # Find caller from where originated the logged message
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
    
    # Replace standard library root logger handler
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("motor").setLevel(logging.WARNING)
    logging.getLogger("pymongo").setLevel(logging.WARNING)
    
    logger.info(f"✅ Logging configured - Level: {log_level}, File: {log_file}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def setup_structured_logging(log_file: str = "structured.log"):
    """
    Setup structured JSON logging for machine processing
    
    Args:
        log_file: Path to structured log file
    """
    import json
    from datetime import datetime
    
    def json_formatter(record):
        """Format log record as JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record["level"].name,
            "logger": record["name"],
            "function": record["function"],
            "line": record["line"],
            "message": record["message"],
        }
        
        # Add exception info if present
        if record["exception"]:
            log_entry["exception"] = {
                "type": record["exception"].type.__name__,
                "value": str(record["exception"].value),
                "traceback": record["exception"].traceback
            }
        
        # Add extra fields
        if "extra" in record:
            log_entry.update(record["extra"])
        
        return json.dumps(log_entry)
    
    # Add structured logging handler
    logger.add(
        log_file,
        format=json_formatter,
        level="INFO",
        rotation="20 MB",
        retention="30 days",
        compression="zip"
    )


def setup_performance_logging(log_file: str = "performance.log"):
    """
    Setup performance logging for monitoring API performance
    
    Args:
        log_file: Path to performance log file
    """
    perf_logger = logging.getLogger("performance")
    perf_logger.setLevel(logging.INFO)
    
    # Create file handler with rotation
    handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    
    # Performance log format
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s'
    )
    handler.setFormatter(formatter)
    perf_logger.addHandler(handler)
    
    return perf_logger


def log_performance(operation: str, duration: float, **kwargs):
    """
    Log performance metrics
    
    Args:
        operation: Operation name
        duration: Duration in seconds
        **kwargs: Additional metrics
    """
    perf_logger = logging.getLogger("performance")
    
    metrics = {
        "operation": operation,
        "duration": duration,
        **kwargs
    }
    
    perf_logger.info(f"PERFORMANCE: {metrics}")


def setup_audit_logging(log_file: str = "audit.log"):
    """
    Setup audit logging for tracking important actions
    
    Args:
        log_file: Path to audit log file
    """
    audit_logger = logging.getLogger("audit")
    audit_logger.setLevel(logging.INFO)
    
    # Create file handler (no rotation for audit logs)
    handler = logging.FileHandler(log_file)
    
    # Audit log format
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s'
    )
    handler.setFormatter(formatter)
    audit_logger.addHandler(handler)
    
    return audit_logger


def log_audit_event(event_type: str, user_id: str = None, details: dict = None):
    """
    Log audit event
    
    Args:
        event_type: Type of event (e.g., "story_scraped", "content_enhanced")
        user_id: User identifier (if applicable)
        details: Additional event details
    """
    audit_logger = logging.getLogger("audit")
    
    event = {
        "event_type": event_type,
        "user_id": user_id,
        "timestamp": "2024-01-01T00:00:00Z",  # Will be set by formatter
        "details": details or {}
    }
    
    audit_logger.info(f"AUDIT: {event}")


class LoggerMixin:
    """Mixin class to add logging capabilities to other classes"""
    
    @property
    def logger(self):
        """Get logger for this class"""
        if not hasattr(self, '_logger'):
            self._logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        return self._logger
    
    def log_info(self, message: str, **kwargs):
        """Log info message with context"""
        self.logger.info(message, extra=kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self.logger.warning(message, extra=kwargs)
    
    def log_error(self, message: str, exc_info: bool = True, **kwargs):
        """Log error message with context"""
        self.logger.error(message, exc_info=exc_info, extra=kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self.logger.debug(message, extra=kwargs)
