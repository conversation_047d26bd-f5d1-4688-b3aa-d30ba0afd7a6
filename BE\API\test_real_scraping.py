#!/usr/bin/env python3
"""
Test Real Scraping Functionality

This script tests the real scraping functionality with the server running.
"""

import requests
import json
import time

def test_story_scraping():
    """Test story information scraping"""
    print("🔍 Testing Story Information Scraping...")
    
    url = "http://localhost:8000/api/v1/scraping/story-info"
    payload = {
        "story_url": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/",
        "include_chapters": True,
        "max_chapters": 3
    }
    
    try:
        print(f"   Sending request to: {url}")
        print(f"   Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get("success"):
                title = data.get("title", "Unknown")
                total_chapters = data.get("total_chapters", 0)
                
                print(f"✅ Successfully scraped story: '{title}'")
                print(f"   Total chapters: {total_chapters}")
                
                # Check if it's real data
                if "Mock" not in title and title != "Unknown":
                    print("✅ Real data scraped (not mock)")
                    
                    # Check Vietnamese content
                    if any(ord(char) > 127 for char in title):
                        print("✅ Vietnamese characters properly handled")
                    
                    return True
                else:
                    print("❌ Mock or invalid data returned")
                    return False
            else:
                print(f"❌ Request failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_chapter_scraping():
    """Test chapter content scraping"""
    print("\n🔍 Testing Chapter Content Scraping...")
    
    url = "http://localhost:8000/api/v1/scraping/batch-chapters"
    payload = {
        "chapter_urls": [
            "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-01-tran-binh-an/"
        ],
        "max_concurrent": 1,
        "rate_limit_delay": 2.0
    }
    
    try:
        print(f"   Sending request to: {url}")
        print(f"   Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get("success"):
                job_id = data.get("job_id")
                print(f"✅ Chapter scraping job created: {job_id}")
                
                # Wait and check job status
                print("   Waiting for job to complete...")
                time.sleep(10)
                
                job_url = f"http://localhost:8000/api/v1/jobs/{job_id}"
                job_response = requests.get(job_url, timeout=10)
                
                if job_response.status_code == 200:
                    job_data = job_response.json()
                    print(f"   Job Status: {json.dumps(job_data, indent=2, ensure_ascii=False)}")
                    
                    status = job_data.get("status", "unknown")
                    completed = job_data.get("completed_items", 0)
                    
                    if status in ["completed", "completed_with_errors"] and completed > 0:
                        print(f"✅ Job completed successfully: {completed} chapters scraped")
                        return True
                    else:
                        print(f"❌ Job not completed properly: status={status}, completed={completed}")
                        return False
                else:
                    print(f"❌ Job status check failed: {job_response.status_code}")
                    return False
            else:
                print(f"❌ Job creation failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_data_retrieval():
    """Test data retrieval after scraping"""
    print("\n🔍 Testing Data Retrieval...")
    
    try:
        # Test stories list
        url = "http://localhost:8000/api/v1/data/stories"
        response = requests.get(url, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get("success"):
                stories = data.get("data", [])
                print(f"✅ Retrieved {len(stories)} stories from database")
                
                # Show story details if any
                for story in stories[:2]:  # Show first 2 stories
                    title = story.get("title", "Unknown")
                    print(f"   - {title}")
                
                return True
            else:
                print("❌ Data retrieval failed")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def main():
    print("🚀 Testing Real Scraping Functionality")
    print("="*60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running or not healthy")
            return False
        print("✅ Server is running and healthy")
    except:
        print("❌ Cannot connect to server. Make sure it's running on localhost:8000")
        return False
    
    # Run tests
    tests = [
        ("Story Scraping", test_story_scraping),
        ("Chapter Scraping", test_chapter_scraping),
        ("Data Retrieval", test_data_retrieval)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All real scraping tests passed!")
        print("✅ The application successfully retrieves real data from Vietnamese web novel sources")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
