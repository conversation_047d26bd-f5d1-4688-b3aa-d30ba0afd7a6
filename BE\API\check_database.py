#!/usr/bin/env python3
"""
Check Database Script

This script checks what's actually stored in the database.
"""

import pymongo
from bson import ObjectId

def check_database():
    """Check database contents"""
    print("🔍 Checking Database Contents")
    print("="*60)
    
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017")
        db = client["webtruyen_api"]
        
        # Check stories
        stories_collection = db["stories"]
        stories = list(stories_collection.find({}))
        
        print(f"📚 Stories in database: {len(stories)}")
        
        for i, story in enumerate(stories):
            print(f"\n📖 Story {i+1}:")
            print(f"   ID: {story.get('_id')}")
            print(f"   Title: {story.get('title')}")
            print(f"   URL: {story.get('url')}")
            print(f"   Slug: {story.get('slug')}")
            print(f"   Status: {story.get('scraping_status')}")
            
            metadata = story.get('metadata', {})
            print(f"   Metadata:")
            print(f"     Author: {metadata.get('author')}")
            print(f"     Description: {metadata.get('description')}")
            print(f"     Total chapters: {metadata.get('total_chapters')}")
            print(f"     Source: {metadata.get('source_website')}")
        
        # Check chapters
        chapters_collection = db["chapters"]
        chapters = list(chapters_collection.find({}))
        
        print(f"\n📄 Chapters in database: {len(chapters)}")
        
        for i, chapter in enumerate(chapters[:5]):  # Show first 5
            print(f"\n📄 Chapter {i+1}:")
            print(f"   ID: {chapter.get('_id')}")
            print(f"   Story ID: {chapter.get('story_id')}")
            print(f"   Chapter Number: {chapter.get('chapter_number')}")
            print(f"   Title: {chapter.get('title')}")
            print(f"   URL: {chapter.get('url')}")
            print(f"   Content length: {len(chapter.get('original_content', ''))}")
        
        # Check jobs
        jobs_collection = db["scraping_jobs"]
        jobs = list(jobs_collection.find({}))
        
        print(f"\n🔧 Jobs in database: {len(jobs)}")
        
        for i, job in enumerate(jobs[-3:]):  # Show last 3
            print(f"\n🔧 Job {i+1}:")
            print(f"   ID: {job.get('_id')}")
            print(f"   Story ID: {job.get('story_id')}")
            print(f"   Status: {job.get('status')}")
            print(f"   Type: {job.get('job_type')}")
            print(f"   Total items: {job.get('total_items')}")
            print(f"   Completed: {job.get('completed_items')}")
            print(f"   Failed: {job.get('failed_items')}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

if __name__ == "__main__":
    check_database()
