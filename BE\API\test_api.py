"""
Basic API Tests

This module contains basic tests to verify the API functionality.
Run with: python -m pytest test_api.py -v
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient

from main import app


class TestAPI:
    """Test cases for the API endpoints"""
    
    def test_root_endpoint(self):
        """Test the root endpoint"""
        with TestClient(app) as client:
            response = client.get("/")
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "Vietnamese Web Novel API"
            assert "version" in data
    
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        with TestClient(app) as client:
            response = client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "message" in data
    
    def test_docs_endpoint(self):
        """Test that API documentation is accessible"""
        with TestClient(app) as client:
            response = client.get("/docs")
            # Should return HTML for docs or redirect
            assert response.status_code in [200, 307]
    
    def test_story_list_endpoint(self):
        """Test the story list endpoint"""
        with Test<PERSON>lient(app) as client:
            response = client.get("/api/v1/data/stories")
            # Should return 200 even if no stories exist
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "pagination" in data
            assert "data" in data
    
    def test_story_list_with_pagination(self):
        """Test story list with pagination parameters"""
        with TestClient(app) as client:
            response = client.get("/api/v1/data/stories?page=1&page_size=10")
            assert response.status_code == 200
            data = response.json()
            assert data["pagination"]["page"] == 1
            assert data["pagination"]["page_size"] == 10
    
    def test_invalid_story_id(self):
        """Test accessing non-existent story"""
        with TestClient(app) as client:
            response = client.get("/api/v1/data/stories/invalid_id")
            assert response.status_code == 404
    
    def test_scraping_test_endpoint(self):
        """Test the scraping service test endpoint"""
        with TestClient(app) as client:
            response = client.get("/api/v1/scraping/test-scraping")
            # May fail if scraping service is not configured, but should not crash
            assert response.status_code in [200, 503]
    
    def test_enhancement_test_endpoint(self):
        """Test the enhancement service test endpoint"""
        with TestClient(app) as client:
            response = client.get("/api/v1/enhancement/test-enhancement")
            # May fail if AI service is not configured, but should not crash
            assert response.status_code in [200, 503]
    
    def test_job_statistics_endpoint(self):
        """Test the job statistics endpoint"""
        with TestClient(app) as client:
            response = client.get("/api/v1/jobs/statistics")
            assert response.status_code == 200
            data = response.json()
            assert "scraping_jobs" in data
            assert "enhancement_jobs" in data
    
    def test_search_suggestions_endpoint(self):
        """Test the search suggestions endpoint"""
        with TestClient(app) as client:
            response = client.get("/api/v1/search/suggestions?q=test")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "suggestions" in data
    
    def test_invalid_export_request(self):
        """Test export with invalid request"""
        with TestClient(app) as client:
            response = client.post("/api/v1/export/", json={
                "export_format": "json"
                # Missing required story_ids or chapter_ids
            })
            assert response.status_code == 400
    
    def test_rate_limiting_headers(self):
        """Test that rate limiting headers are present"""
        with TestClient(app) as client:
            response = client.get("/api/v1/data/stories")
            # Check for rate limiting headers
            assert "X-RateLimit-Limit" in response.headers
            assert "X-RateLimit-Remaining" in response.headers
    
    def test_cors_headers(self):
        """Test CORS headers are present"""
        with TestClient(app) as client:
            response = client.options("/api/v1/data/stories")
            # Should have CORS headers
            assert response.status_code in [200, 405]  # Some may not support OPTIONS
    
    def test_error_response_format(self):
        """Test that error responses follow the standard format"""
        with TestClient(app) as client:
            response = client.get("/api/v1/data/stories/invalid_id")
            assert response.status_code == 404
            data = response.json()
            assert data["success"] is False
            assert "message" in data
            assert "timestamp" in data


@pytest.mark.asyncio
class TestAsyncAPI:
    """Async test cases for the API"""
    
    async def test_async_client(self):
        """Test using async client"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


def test_configuration():
    """Test that configuration is loaded properly"""
    from config import get_settings
    
    settings = get_settings()
    assert settings.app_name == "Vietnamese Web Novel API"
    assert settings.version == "1.0.0"
    assert settings.host is not None
    assert settings.port is not None


def test_database_models():
    """Test that database models can be imported and instantiated"""
    from models.database import Story, Chapter, StoryStatus
    
    # Test model creation
    story_data = {
        "title": "Test Story",
        "url": "https://example.com/story",
        "metadata": {
            "status": StoryStatus.ONGOING
        }
    }
    
    # Should not raise an exception
    story = Story(**story_data)
    assert story.title == "Test Story"


def test_api_models():
    """Test that API models work correctly"""
    from models.api_models import StoryListRequest, APIResponse
    
    # Test request model
    request = StoryListRequest(page=1, page_size=20)
    assert request.page == 1
    assert request.page_size == 20
    
    # Test response model
    response = APIResponse(success=True, message="Test message")
    assert response.success is True
    assert response.message == "Test message"


if __name__ == "__main__":
    # Run basic tests
    print("Running basic API tests...")
    
    # Test configuration
    try:
        test_configuration()
        print("✅ Configuration test passed")
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
    
    # Test models
    try:
        test_database_models()
        test_api_models()
        print("✅ Model tests passed")
    except Exception as e:
        print(f"❌ Model tests failed: {e}")
    
    # Test basic endpoints
    try:
        test_client = TestClient(app)
        
        # Test root
        response = test_client.get("/")
        assert response.status_code == 200
        print("✅ Root endpoint test passed")
        
        # Test health
        response = test_client.get("/health")
        assert response.status_code == 200
        print("✅ Health endpoint test passed")
        
        print("🎉 Basic tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Endpoint tests failed: {e}")
        print("Note: Some tests may fail if database is not connected or services are not configured.")
