#!/usr/bin/env python3
"""
Test Batch Processing Functionality

This script tests the batch processing capabilities of the web scraping API.
"""

import requests
import json
import time

def test_batch_chapter_scraping():
    """Test batch chapter scraping with multiple URLs"""
    print("🔍 Testing Batch Chapter Scraping...")
    
    url = "http://localhost:8000/api/v1/scraping/batch-chapters"
    
    # Test with multiple chapter URLs
    chapter_urls = [
        "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-566-troi-cho-ma-khong-lay-phan-thu-ky-cuu-cau-nguyet-phieu-2/",
        "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-565-uan-duong-chua-thuong-chien-luc-phan-dinh-cau-nguyet-phieu-2/",
        "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-564-kinh-loi-mot-quyen-vo-dao-chi-tam-cau-nguyet-phieu/"
    ]
    
    payload = {
        "chapter_urls": chapter_urls,
        "max_concurrent": 2,
        "rate_limit_delay": 3.0
    }
    
    try:
        print(f"   Sending batch request for {len(chapter_urls)} chapters")
        print(f"   Max concurrent: {payload['max_concurrent']}")
        print(f"   Rate limit delay: {payload['rate_limit_delay']}s")
        
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get("success"):
                job_id = data.get("job_id")
                total_chapters = data.get("total_chapters", 0)
                
                print(f"✅ Batch job created successfully")
                print(f"   Job ID: {job_id}")
                print(f"   Total chapters to process: {total_chapters}")
                
                # Monitor job progress
                return monitor_job_progress(job_id, expected_chapters=total_chapters)
            else:
                print(f"❌ Batch job creation failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def monitor_job_progress(job_id, expected_chapters=0, max_wait_time=180):
    """Monitor job progress and return final status"""
    print(f"\n📊 Monitoring job progress: {job_id}")
    
    start_time = time.time()
    last_completed = 0
    
    while time.time() - start_time < max_wait_time:
        try:
            # Check job status
            job_url = f"http://localhost:8000/api/v1/jobs/{job_id}"
            response = requests.get(job_url, timeout=10)
            
            if response.status_code == 200:
                job_data = response.json()
                
                status = job_data.get("status", "unknown")
                completed = job_data.get("completed_items", 0)
                failed = job_data.get("failed_items", 0)
                total = job_data.get("total_items", expected_chapters)
                
                # Show progress if changed
                if completed != last_completed:
                    print(f"   Progress: {completed}/{total} completed, {failed} failed - Status: {status}")
                    last_completed = completed
                
                # Check if job is finished
                if status in ["completed", "completed_with_errors"]:
                    print(f"✅ Job completed!")
                    print(f"   Final status: {status}")
                    print(f"   Completed: {completed}/{total}")
                    print(f"   Failed: {failed}")
                    
                    if completed > 0:
                        print("✅ Batch processing successful")
                        return True
                    else:
                        print("❌ No chapters were successfully processed")
                        return False
                
                elif status == "failed":
                    print(f"❌ Job failed with status: {status}")
                    return False
                
                # Wait before next check
                time.sleep(5)
                
            elif response.status_code == 404:
                print(f"❌ Job not found: {job_id}")
                return False
            else:
                print(f"❌ Error checking job status: {response.status_code}")
                time.sleep(5)
                
        except Exception as e:
            print(f"❌ Error monitoring job: {e}")
            time.sleep(5)
    
    print(f"⏰ Job monitoring timed out after {max_wait_time}s")
    return False

def test_concurrent_requests():
    """Test concurrent API requests to verify rate limiting"""
    print("\n🔍 Testing Concurrent Requests and Rate Limiting...")
    
    import threading
    import time
    
    results = []
    start_time = time.time()
    
    def make_request(request_id):
        try:
            response = requests.get("http://localhost:8000/api/v1/data/stories", timeout=10)
            end_time = time.time()
            results.append({
                "id": request_id,
                "status": response.status_code,
                "time": end_time - start_time,
                "rate_limited": response.status_code == 429
            })
        except Exception as e:
            results.append({
                "id": request_id,
                "status": "error",
                "time": time.time() - start_time,
                "error": str(e)
            })
    
    # Launch 10 concurrent requests
    threads = []
    for i in range(10):
        thread = threading.Thread(target=make_request, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Analyze results
    total_requests = len(results)
    successful = sum(1 for r in results if r.get("status") == 200)
    rate_limited = sum(1 for r in results if r.get("rate_limited", False))
    errors = sum(1 for r in results if r.get("status") == "error")
    
    print(f"   Total requests: {total_requests}")
    print(f"   Successful (200): {successful}")
    print(f"   Rate limited (429): {rate_limited}")
    print(f"   Errors: {errors}")
    
    if rate_limited > 0:
        print("✅ Rate limiting is working")
    else:
        print("⚠️  No rate limiting detected")
    
    if successful > 0:
        print("✅ Concurrent requests handled successfully")
        return True
    else:
        print("❌ No successful concurrent requests")
        return False

def test_large_batch_processing():
    """Test processing a larger batch of chapters"""
    print("\n🔍 Testing Large Batch Processing...")
    
    # First get a story with many chapters
    story_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    try:
        # Get story info to get chapter list
        payload = {
            "story_url": story_url,
            "include_chapters": True,
            "max_chapters": 10  # Get 10 chapters for batch processing
        }
        
        response = requests.post("http://localhost:8000/api/v1/scraping/story-info", json=payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("chapters"):
                chapters = data.get("chapters", [])
                chapter_urls = [chapter["url"] for chapter in chapters[:5]]  # Take first 5 chapters
                
                print(f"   Processing {len(chapter_urls)} chapters from story: {data.get('title')}")
                
                # Now batch process these chapters
                batch_payload = {
                    "chapter_urls": chapter_urls,
                    "max_concurrent": 3,
                    "rate_limit_delay": 2.0
                }
                
                batch_response = requests.post("http://localhost:8000/api/v1/scraping/batch-chapters", 
                                             json=batch_payload, timeout=120)
                
                if batch_response.status_code == 200:
                    batch_data = batch_response.json()
                    if batch_data.get("success"):
                        job_id = batch_data.get("job_id")
                        print(f"✅ Large batch job created: {job_id}")
                        
                        # Monitor the job
                        return monitor_job_progress(job_id, expected_chapters=len(chapter_urls), max_wait_time=300)
                    else:
                        print(f"❌ Large batch job creation failed: {batch_data.get('message')}")
                        return False
                else:
                    print(f"❌ Large batch request failed: {batch_response.status_code}")
                    return False
            else:
                print("❌ Could not get chapter list for large batch test")
                return False
        else:
            print(f"❌ Story info request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Large batch test failed: {e}")
        return False

def main():
    print("🚀 Testing Batch Processing Functionality")
    print("="*60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running or not healthy")
            return False
        print("✅ Server is running and healthy")
    except:
        print("❌ Cannot connect to server. Make sure it's running on localhost:8000")
        return False
    
    # Run batch processing tests
    tests = [
        ("Batch Chapter Scraping", test_batch_chapter_scraping),
        ("Concurrent Requests", test_concurrent_requests),
        ("Large Batch Processing", test_large_batch_processing)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 BATCH PROCESSING TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All batch processing tests passed!")
        print("✅ Batch processing capabilities are working correctly")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
