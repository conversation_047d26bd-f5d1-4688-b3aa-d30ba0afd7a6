"""
Scraping Service

This service provides high-level scraping operations for stories and chapters.
For now, it provides a mock implementation until the scraper integration is fixed.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from utils.logging_config import LoggerMixin
from middleware.error_handling import ScrapingError
from config import get_settings


class ScrapingService(LoggerMixin):
    """Service for web scraping operations (Mock implementation)"""

    def __init__(self):
        self.settings = get_settings()
        self._initialized = False

    async def _initialize(self):
        """Initialize scraper components"""
        if self._initialized:
            return

        try:
            self.log_info("Initializing scraping service (mock)...")
            self._initialized = True
            self.log_info("✅ Scraping service initialized successfully (mock)")

        except Exception as e:
            self.log_error(f"Failed to initialize scraping service: {e}")
            raise ScrapingError(f"Scraping service initialization failed: {e}")

    async def _cleanup(self):
        """Cleanup scraper resources"""
        try:
            self._initialized = False
            self.log_info("✅ Scraping service cleaned up (mock)")

        except Exception as e:
            self.log_error(f"Error during scraping service cleanup: {e}")
    
    async def scrape_story_info(
        self,
        story_url: str,
        include_chapters: bool = True,
        max_chapters: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Scrape story information and chapter list (Mock implementation)

        Args:
            story_url: URL of the story page
            include_chapters: Whether to include chapter list
            max_chapters: Maximum number of chapters to include

        Returns:
            Dictionary containing story information and chapters
        """
        await self._initialize()

        try:
            self.log_info(f"Mock scraping story info from: {story_url}")

            # Mock story data
            result = {
                "url": story_url,
                "title": "Mock Story Title",
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "scraped_at": datetime.utcnow().isoformat(),
                    "source_website": "webtruyen",
                    "total_chapters": 10,
                    "status": "ongoing",
                    "author": "Mock Author",
                    "description": "This is a mock story for testing purposes."
                }
            }

            # Add mock chapters if requested
            if include_chapters:
                chapters = []
                num_chapters = min(max_chapters or 10, 10)

                for i in range(1, num_chapters + 1):
                    chapters.append({
                        "title": f"Chapter {i}: Mock Chapter Title",
                        "url": f"{story_url}/chapter-{i}",
                        "chapter_number": i
                    })

                result["chapters"] = chapters
                result["metadata"]["included_chapters"] = len(chapters)

            self.log_info(f"Successfully mock scraped story info: {result['title']} ({len(result.get('chapters', []))} chapters)")
            return result

        except Exception as e:
            self.log_error(f"Error mock scraping story info from {story_url}: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}", url=story_url)
    
    async def scrape_chapter_content(self, chapter_url: str) -> Dict[str, Any]:
        """
        Scrape content from a single chapter (Mock implementation)

        Args:
            chapter_url: URL of the chapter page

        Returns:
            Dictionary containing chapter content and metadata
        """
        await self._initialize()

        try:
            self.log_info(f"Mock scraping chapter content from: {chapter_url}")
            start_time = datetime.utcnow()

            # Mock chapter content
            mock_content = """
            Đây là nội dung chương mock để test API.

            Trong chương này, nhân vật chính đang trải qua những thử thách khó khăn.
            Anh ta phải đối mặt với nhiều kẻ thù và vượt qua những trở ngại lớn.

            Câu chuyện tiếp tục phát triển với những tình tiết hấp dẫn và bất ngờ.
            Người đọc sẽ được thưởng thức những pha hành động gay cấn và cảm xúc sâu sắc.

            Đây chỉ là nội dung mock để test hệ thống API scraping.
            """

            # Calculate scraping duration
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Build result
            result = {
                "url": chapter_url,
                "title": "Mock Chapter Title",
                "content": mock_content.strip(),
                "is_locked": False,
                "scraping_duration": duration,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "scraped_at": end_time.isoformat(),
                    "word_count": len(mock_content.split()),
                    "character_count": len(mock_content),
                    "source_url": chapter_url,
                    "navigation": {},
                    "scraping_metadata": {}
                }
            }

            self.log_info(f"Successfully mock scraped chapter: {result['title']} ({result['metadata']['word_count']} words)")
            return result

        except Exception as e:
            self.log_error(f"Error mock scraping chapter content from {chapter_url}: {e}")
            raise ScrapingError(f"Failed to scrape chapter content: {e}", url=chapter_url)
    
    async def scrape_multiple_chapters(
        self,
        chapter_urls: List[str],
        max_concurrent: int = 3,
        rate_limit_delay: float = 2.0
    ) -> List[Dict[str, Any]]:
        """
        Scrape content from multiple chapters concurrently
        
        Args:
            chapter_urls: List of chapter URLs to scrape
            max_concurrent: Maximum concurrent scraping operations
            rate_limit_delay: Delay between requests in seconds
            
        Returns:
            List of chapter data dictionaries
        """
        await self._initialize()
        
        try:
            self.log_info(f"Starting batch scraping of {len(chapter_urls)} chapters")
            
            results = []
            failed_urls = []
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def scrape_single_with_delay(url: str) -> Optional[Dict[str, Any]]:
                """Scrape single chapter with rate limiting"""
                async with semaphore:
                    try:
                        # Add delay for rate limiting
                        await asyncio.sleep(rate_limit_delay)
                        
                        # Scrape chapter
                        chapter_data = await self.scrape_chapter_content(url)
                        return chapter_data
                        
                    except Exception as e:
                        self.log_error(f"Failed to scrape chapter {url}: {e}")
                        failed_urls.append(url)
                        return None
            
            # Execute scraping tasks
            tasks = [scrape_single_with_delay(url) for url in chapter_urls]
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in task_results:
                if result and not isinstance(result, Exception):
                    results.append(result)
            
            success_count = len(results)
            failed_count = len(failed_urls)
            
            self.log_info(f"Batch scraping completed: {success_count} success, {failed_count} failed")
            
            return results
            
        except Exception as e:
            self.log_error(f"Error in batch chapter scraping: {e}")
            raise ScrapingError(f"Batch scraping failed: {e}")
    
    async def test_connection(self) -> str:
        """Test scraping service connection and functionality (Mock)"""
        try:
            await self._initialize()

            if self._initialized:
                return "Mock scraping service connected and ready"
            else:
                return "Mock connection failed"

        except Exception as e:
            self.log_error(f"Mock connection test failed: {e}")
            return f"Mock connection failed: {e}"
    
    async def get_scraping_stats(self) -> Dict[str, Any]:
        """Get scraping service statistics"""
        return {
            "initialized": self._initialized,
            "config_path": str(self.config_path),
            "max_concurrent": self.settings.max_concurrent_scraping,
            "rate_limit_delay": self.settings.scraping_delay_min,
            "timeout": self.settings.scraping_timeout
        }
    
    def __del__(self):
        """Cleanup on destruction"""
        if self._initialized:
            try:
                # Try to cleanup if event loop is available
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._cleanup())
                else:
                    loop.run_until_complete(self._cleanup())
            except:
                pass  # Ignore cleanup errors during destruction


# ============================================================================
# Service Factory
# ============================================================================

_scraping_service_instance: Optional[ScrapingService] = None


def get_scraping_service() -> ScrapingService:
    """Get singleton scraping service instance"""
    global _scraping_service_instance
    
    if _scraping_service_instance is None:
        _scraping_service_instance = ScrapingService()
    
    return _scraping_service_instance


async def cleanup_scraping_service():
    """Cleanup scraping service on application shutdown"""
    global _scraping_service_instance
    
    if _scraping_service_instance:
        await _scraping_service_instance._cleanup()
        _scraping_service_instance = None
