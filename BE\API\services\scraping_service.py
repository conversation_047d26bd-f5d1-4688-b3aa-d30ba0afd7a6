"""
Scraping Service

This service provides high-level scraping operations for stories and chapters.
Integrates with the real MetruyenScraper for actual web scraping functionality.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from utils.logging_config import LoggerMixin
from middleware.error_handling import Scraping<PERSON>rror
from config import get_settings


class ScrapingService(LoggerMixin):
    """Service for web scraping operations using real MetruyenScraper"""

    def __init__(self):
        self.settings = get_settings()
        self._initialized = False
        self._scraper = None
        self._scraper_path = Path(__file__).parent.parent.parent / "Scraper"

    async def _initialize(self):
        """Initialize scraper components"""
        if self._initialized:
            return

        try:
            self.log_info("Initializing real scraping service...")

            # Add scraper path to sys.path
            if str(self._scraper_path) not in sys.path:
                sys.path.insert(0, str(self._scraper_path))

            # Import and initialize the real scraper
            from src.metruyenscraper import <PERSON><PERSON>yen<PERSON><PERSON>raper

            # Initialize scraper with config
            config_path = self._scraper_path / "config.yaml"
            self._scraper = MetruyenScraper(str(config_path))
            await self._scraper.start()

            self._initialized = True
            self.log_info("✅ Real scraping service initialized successfully")

        except Exception as e:
            self.log_error(f"Failed to initialize scraping service: {e}")
            raise ScrapingError(f"Scraping service initialization failed: {e}")

    async def _cleanup(self):
        """Cleanup scraper resources"""
        try:
            if self._scraper:
                await self._scraper.close()
                self._scraper = None
            self._initialized = False
            self.log_info("✅ Scraping service cleaned up")

        except Exception as e:
            self.log_error(f"Error during scraping service cleanup: {e}")
    
    async def scrape_story_info(
        self,
        story_url: str,
        include_chapters: bool = True,
        max_chapters: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Scrape story information and chapter list using real scraper

        Args:
            story_url: URL of the story page
            include_chapters: Whether to include chapter list
            max_chapters: Maximum number of chapters to include

        Returns:
            Dictionary containing story information and chapters
        """
        await self._initialize()

        try:
            self.log_info(f"Scraping story info from: {story_url}")

            if include_chapters:
                # Use scrape_story_chapters to get both story info and chapters
                chapter_results = await self._scraper.scrape_story_chapters(
                    story_url,
                    target_name="webtruyen",
                    max_chapters=max_chapters,
                    interactive=False,
                    start_chapter=1,
                    end_chapter=max_chapters
                )

                # Extract story metadata from first chapter or scrape story page directly
                story_data = await self._scraper.scrape_url(story_url, "webtruyen")

                if not story_data:
                    raise ScrapingError("Failed to scrape story page", url=story_url)

                # Build result with chapters
                chapters = []
                for i, chapter_data in enumerate(chapter_results):
                    chapters.append({
                        "title": chapter_data.get("title", f"Chapter {i+1}"),
                        "url": chapter_data.get("url", ""),
                        "chapter_number": chapter_data.get("chapter_number", i+1)
                    })

                result = {
                    "url": story_url,
                    "title": story_data.get("title", "Unknown Title"),
                    "timestamp": datetime.utcnow().isoformat(),
                    "chapters": chapters,
                    "metadata": {
                        "scraped_at": datetime.utcnow().isoformat(),
                        "source_website": "webtruyen",
                        "total_chapters": len(chapters),
                        "included_chapters": len(chapters),
                        "status": "ongoing",
                        "author": story_data.get("metadata", {}).get("author", "Unknown"),
                        "description": story_data.get("metadata", {}).get("description", "")
                    }
                }
            else:
                # Just scrape story page without chapters
                story_data = await self._scraper.scrape_url(story_url, "webtruyen")

                if not story_data:
                    raise ScrapingError("Failed to scrape story page", url=story_url)

                result = {
                    "url": story_url,
                    "title": story_data.get("title", "Unknown Title"),
                    "timestamp": datetime.utcnow().isoformat(),
                    "metadata": {
                        "scraped_at": datetime.utcnow().isoformat(),
                        "source_website": "webtruyen",
                        "status": "ongoing",
                        "author": story_data.get("metadata", {}).get("author", "Unknown"),
                        "description": story_data.get("metadata", {}).get("description", "")
                    }
                }

            self.log_info(f"Successfully scraped story info: {result['title']} ({len(result.get('chapters', []))} chapters)")
            return result

        except Exception as e:
            self.log_error(f"Error scraping story info from {story_url}: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}", url=story_url)
    
    async def scrape_chapter_content(self, chapter_url: str) -> Dict[str, Any]:
        """
        Scrape content from a single chapter using real scraper

        Args:
            chapter_url: URL of the chapter page

        Returns:
            Dictionary containing chapter content and metadata
        """
        await self._initialize()

        try:
            self.log_info(f"Scraping chapter content from: {chapter_url}")
            start_time = datetime.utcnow()

            # Use real scraper to get chapter content
            chapter_data = await self._scraper.scrape_url(chapter_url, "webtruyen")

            if not chapter_data:
                raise ScrapingError("Failed to scrape chapter content", url=chapter_url)

            # Calculate scraping duration
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Extract content and metadata
            content = chapter_data.get("content", "")
            title = chapter_data.get("title", "Unknown Chapter")
            is_locked = chapter_data.get("is_locked", False)
            navigation = chapter_data.get("navigation", {})

            # Build result
            result = {
                "url": chapter_url,
                "title": title,
                "content": content,
                "is_locked": is_locked,
                "scraping_duration": duration,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "scraped_at": end_time.isoformat(),
                    "word_count": len(content.split()) if content else 0,
                    "character_count": len(content) if content else 0,
                    "source_url": chapter_url,
                    "navigation": navigation,
                    "scraping_metadata": chapter_data.get("metadata", {})
                }
            }

            self.log_info(f"Successfully scraped chapter: {title} ({result['metadata']['word_count']} words)")
            return result

        except Exception as e:
            self.log_error(f"Error scraping chapter content from {chapter_url}: {e}")
            raise ScrapingError(f"Failed to scrape chapter content: {e}", url=chapter_url)
    
    async def scrape_multiple_chapters(
        self,
        chapter_urls: List[str],
        max_concurrent: int = 3,
        rate_limit_delay: float = 2.0
    ) -> List[Dict[str, Any]]:
        """
        Scrape content from multiple chapters concurrently using real scraper

        Args:
            chapter_urls: List of chapter URLs to scrape
            max_concurrent: Maximum concurrent scraping operations
            rate_limit_delay: Delay between requests in seconds

        Returns:
            List of chapter data dictionaries
        """
        await self._initialize()

        try:
            self.log_info(f"Starting batch scraping of {len(chapter_urls)} chapters")

            # Use the scraper's built-in batch scraping functionality
            chapter_results = await self._scraper.scrape_urls(
                chapter_urls,
                target_name="webtruyen",
                max_concurrent=max_concurrent
            )

            # Convert results to expected format
            results = []
            for chapter_data in chapter_results:
                if chapter_data:
                    content = chapter_data.get("content", "")
                    title = chapter_data.get("title", "Unknown Chapter")
                    url = chapter_data.get("url", "")
                    is_locked = chapter_data.get("is_locked", False)
                    navigation = chapter_data.get("navigation", {})

                    result = {
                        "url": url,
                        "title": title,
                        "content": content,
                        "is_locked": is_locked,
                        "scraping_duration": 0,  # Not tracked in batch mode
                        "timestamp": datetime.utcnow().isoformat(),
                        "metadata": {
                            "scraped_at": datetime.utcnow().isoformat(),
                            "word_count": len(content.split()) if content else 0,
                            "character_count": len(content) if content else 0,
                            "source_url": url,
                            "navigation": navigation,
                            "scraping_metadata": chapter_data.get("metadata", {})
                        }
                    }
                    results.append(result)

            success_count = len(results)
            failed_count = len(chapter_urls) - success_count

            self.log_info(f"Batch scraping completed: {success_count} success, {failed_count} failed")

            return results

        except Exception as e:
            self.log_error(f"Error in batch chapter scraping: {e}")
            raise ScrapingError(f"Batch scraping failed: {e}")
    
    async def test_connection(self) -> str:
        """Test scraping service connection and functionality"""
        try:
            await self._initialize()

            if self._initialized and self._scraper:
                # Test with a simple URL
                test_url = "https://webtruyen.diendantruyen.com/"
                test_result = await self._scraper.test_target_site(test_url, "webtruyen")

                if test_result.get("success"):
                    return "Real scraping service connected and ready"
                else:
                    return f"Scraping service connected but test failed: {test_result.get('errors', [])}"
            else:
                return "Scraping service initialization failed"

        except Exception as e:
            self.log_error(f"Connection test failed: {e}")
            return f"Connection test failed: {e}"

    async def get_scraping_stats(self) -> Dict[str, Any]:
        """Get scraping service statistics"""
        stats = {
            "initialized": self._initialized,
            "scraper_path": str(self._scraper_path),
            "max_concurrent": self.settings.max_concurrent_scraping,
            "rate_limit_delay": self.settings.scraping_delay_min,
            "timeout": self.settings.scraping_timeout
        }

        if self._scraper and self._initialized:
            try:
                scraper_stats = self._scraper.get_statistics()
                stats.update(scraper_stats)
            except Exception as e:
                self.log_error(f"Failed to get scraper statistics: {e}")
                stats["scraper_stats_error"] = str(e)

        return stats
    
    def __del__(self):
        """Cleanup on destruction"""
        if self._initialized:
            try:
                # Try to cleanup if event loop is available
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._cleanup())
                else:
                    loop.run_until_complete(self._cleanup())
            except:
                pass  # Ignore cleanup errors during destruction


# ============================================================================
# Service Factory
# ============================================================================

_scraping_service_instance: Optional[ScrapingService] = None


def get_scraping_service() -> ScrapingService:
    """Get singleton scraping service instance"""
    global _scraping_service_instance
    
    if _scraping_service_instance is None:
        _scraping_service_instance = ScrapingService()
    
    return _scraping_service_instance


async def cleanup_scraping_service():
    """Cleanup scraping service on application shutdown"""
    global _scraping_service_instance
    
    if _scraping_service_instance:
        await _scraping_service_instance._cleanup()
        _scraping_service_instance = None
