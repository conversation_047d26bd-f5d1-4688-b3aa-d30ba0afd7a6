"""
Core Scraper Engine using <PERSON>wright for dynamic content handling
Supports anti-detection measures and robust error handling
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, <PERSON>
from fake_useragent import UserAgent
from loguru import logger

from .config_manager import ConfigManager
from .anti_detection import AntiDetectionManager
from .data_processor import DataProcessor


class ScraperEngine:
    """
    Core scraper engine using Playwright for dynamic content handling.

    This class manages the browser automation, applies anti-detection measures,
    and handles the actual web scraping operations. It uses <PERSON>wright's
    Chromium browser with stealth configurations to avoid detection.

    Key features:
    - Headless browser automation with Playwright
    - Anti-detection measures (user agent rotation, delays, stealth scripts)
    - Dynamic content waiting and extraction
    - Robust error handling and recovery
    - Support for both single pages and chapter lists
    """

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the scraper engine with configuration.

        Args:
            config_path: Path to YAML configuration file
        """
        self.config = ConfigManager(config_path)
        self.anti_detection = AntiDetectionManager(self.config)
        self.data_processor = DataProcessor(self.config)

        # Browser automation components
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_file = log_config.get('file', 'scraper.log')
        log_format = log_config.get('format', '{time} | {level} | {message}')
        
        logger.add(log_file, level=log_level, format=log_format, rotation="10 MB")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """
        Initialize browser and context with anti-detection measures.

        Sets up a Playwright Chromium browser with:
        - Stealth arguments to avoid automation detection
        - Custom user agent and viewport settings
        - JavaScript stealth scripts to hide automation indicators
        - Optimized settings for performance and reliability
        """
        try:
            playwright = await async_playwright().start()
            browser_config = self.config.get('scraper.browser', {})

            # Launch browser with comprehensive stealth settings
            # These arguments help avoid detection by anti-bot systems
            self.browser = await playwright.chromium.launch(
                headless=browser_config.get('headless', True),
                args=[
                    '--no-sandbox',  # Required for some environments
                    '--disable-blink-features=AutomationControlled',  # Hide automation
                    '--disable-dev-shm-usage',  # Avoid shared memory issues
                    '--disable-extensions',  # Reduce fingerprinting
                    '--disable-plugins',  # Reduce fingerprinting
                    '--disable-images',  # Speed optimization
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection',
                ]
            )

            # Create browser context with anti-detection settings
            context_options = await self.anti_detection.get_context_options()
            self.context = await self.browser.new_context(**context_options)

            # Apply JavaScript stealth scripts to hide automation indicators
            await self.anti_detection.apply_stealth_scripts(self.context)

            # Create the main page for scraping
            self.page = await self.context.new_page()

            logger.info("Scraper engine initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize scraper engine: {e}")
            raise
    
    async def close(self) -> None:
        """Close browser and cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()

            logger.info("Scraper engine closed successfully")

        except Exception as e:
            logger.error(f"Error closing scraper engine: {e}")

    async def scrape_chapters(self, url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Scrape a single URL and extract data"""
        if not self.page:
            raise RuntimeError("Scraper engine not initialized. Use async context manager.")
        
        try:
            logger.info(f"Scraping URL: {url}")

            # Apply anti-detection measures
            await self.anti_detection.randomize_user_agent(self.page)
            await self.anti_detection.add_random_delay()
            
            # Navigate to URL
            timeout = self.config.get('scraper.browser.timeout', 30000)
            response = await self.page.goto(url, timeout=timeout, wait_until='networkidle')
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for dynamic content
            await self._wait_for_content(target_name)
            
            # Extract data
            data = await self._extract_data_list(url, target_name)
            
            logger.info(f"Successfully scraped: {url}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            raise

    async def scrape_url(self, url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Scrape a single URL and extract data"""
        if not self.page:
            raise RuntimeError("Scraper engine not initialized. Use async context manager.")
        
        try:
            logger.info(f"Scraping URL: {url}")

            # Apply anti-detection measures
            await self.anti_detection.randomize_user_agent(self.page)
            await self.anti_detection.add_random_delay()
            
            # Navigate to URL
            timeout = self.config.get('scraper.browser.timeout', 30000)
            response = await self.page.goto(url, timeout=timeout, wait_until='networkidle')
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for dynamic content
            await self._wait_for_content(target_name)
            
            # Extract data
            data = await self._extract_data(url, target_name)
            
            logger.info(f"Successfully scraped: {url}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            raise
    
    async def _wait_for_content(self, target_name: str) -> None:
        """Wait for dynamic content to load"""
        target_config = self.config.get_target_config(target_name)
        wait_conditions = target_config.get('wait_conditions', ['networkidle'])

        # Wait for specific selectors if configured
        selectors = self.config.get_selectors(target_name)

        try:
            # Wait for main content selector
            if 'content' in selectors:
                await self.page.wait_for_selector(selectors['content'], timeout=10000)
                logger.debug("Content selector found")

            # Additional wait for page stability
            delays = self.config.get_delays()
            page_load_delay = delays.get('page_load_delay', 3)
            await asyncio.sleep(page_load_delay)

        except Exception as e:
            logger.warning(f"Content wait timeout: {e}")

        # Debug: Log page structure only in debug mode
        # Check if debug logging is enabled
        try:
            # Use logger._core.min_level to check the minimum logging level
            if hasattr(logger, '_core') and logger._core.min_level <= 10:  # DEBUG level
                await self._debug_page_structure(selectors)
        except Exception:
            # Fallback: always run debug in case of issues checking level
            pass

    async def _debug_page_structure(self, selectors: Dict[str, str]) -> None:
        """Debug page structure and content - only runs in debug mode"""
        try:
            logger.debug("Analyzing page structure...")

            # Get page title
            title = await self.page.title()
            logger.debug(f"Page title: {title}")

            # Check if page is fully loaded
            ready_state = await self.page.evaluate("document.readyState")
            logger.debug(f"Document ready state: {ready_state}")

            # Log content selector results
            content_selector = selectors.get('content', '')
            if content_selector:
                selector_parts = [s.strip() for s in content_selector.split(',')]
                for i, selector in enumerate(selector_parts):
                    try:
                        elements = await self.page.query_selector_all(selector)
                        if elements:
                            logger.debug(f"Selector '{selector}': found {len(elements)} elements")
                    except Exception as e:
                        logger.debug(f"Selector '{selector}' failed: {e}")

            # Take screenshot for debugging
            # screenshot_path = f"screenshots/debug/debug_screenshot_{int(time.time())}.png"
            # await self.page.screenshot(path=screenshot_path)
            # logger.debug(f"Debug screenshot saved: {screenshot_path}")

        except Exception as e:
            logger.debug(f"Debug page structure failed: {e}")

    async def _extract_data(self, url: str, target_name: str) -> Dict[str, Any]:
        """Extract data from current page"""
        selectors = self.config.get_selectors(target_name)
        data = {
            'url': url,
            'timestamp': time.time(),
            'title': None,
            'content': None,
            'metadata': {},
            'navigation': {},
            'is_locked': False
        }
        
        try:
            # Extract title
            if 'title' in selectors:
                title_element = await self.page.query_selector(selectors['title'])
                if title_element:
                    data['title'] = await title_element.inner_text()
            
            # Check if content is locked
            if 'locked_content' in selectors:
                locked_element = await self.page.query_selector(selectors['locked_content'])
                data['is_locked'] = locked_element is not None
                if data['is_locked']:
                    logger.info("Locked content detected")
            else:
                data['is_locked'] = False

            # Extract main content
            if 'content' in selectors:
                # Try each selector part separately
                selector_parts = [s.strip() for s in selectors['content'].split(',')]
                content_found = False

                for selector in selector_parts:
                    try:
                        content_elements = await self.page.query_selector_all(selector)
                        logger.debug(f"Found {len(content_elements)} elements with selector: {selector}")

                        for element in content_elements:
                            try:
                                # Try to preserve formatting by converting HTML to formatted text
                                content = await self._extract_formatted_content(element)
                                
                                if content and content.strip() and len(content.strip()) > 50:
                                    data['content'] = content.strip()
                                    content_found = True
                                    logger.debug(f"Content extracted with preserved formatting")
                                    break

                                if content_found:
                                    break

                                # Try hidden element extraction if needed
                                if not content_found:
                                    try:
                                        is_visible = await element.is_visible()
                                        if not is_visible:
                                            js_content = await element.evaluate("""
                                                el => {
                                                    let content = el.textContent || el.innerText;
                                                    if (!content || content.trim().length === 0) {
                                                        const innerHTML = el.innerHTML;
                                                        if (innerHTML) {
                                                            const tempDiv = document.createElement('div');
                                                            tempDiv.innerHTML = innerHTML;
                                                            content = tempDiv.textContent || tempDiv.innerText || '';
                                                        }
                                                    }
                                                    return content;
                                                }
                                            """)

                                            if js_content and len(js_content.strip()) > 50:
                                                data['content'] = js_content.strip()
                                                content_found = True
                                                logger.debug("Content extracted from hidden element")
                                    except Exception as e:
                                        logger.debug(f"Hidden element extraction failed: {e}")

                                # Special handling for #chapter-content element
                                if not content_found and selector == "#chapter-content":
                                    try:
                                        await asyncio.sleep(2)  # Wait for content to load
                                        innerHTML_content = await element.evaluate("""
                                            el => {
                                                const innerHTML = el.innerHTML;
                                                if (innerHTML && innerHTML.trim().length > 0) {
                                                    const tempDiv = document.createElement('div');
                                                    tempDiv.innerHTML = innerHTML;
                                                    const scripts = tempDiv.querySelectorAll('script, style, canvas');
                                                    scripts.forEach(script => script.remove());
                                                    let textContent = tempDiv.textContent || tempDiv.innerText || '';
                                                    textContent = textContent.replace(/\\s+/g, ' ').trim();
                                                    return textContent;
                                                }
                                                return '';
                                            }
                                        """)

                                        if innerHTML_content and len(innerHTML_content.strip()) > 50:
                                            data['content'] = innerHTML_content.strip()
                                            content_found = True
                                            logger.debug("Content extracted from #chapter-content innerHTML")

                                    except Exception as e:
                                        logger.debug(f"#chapter-content special extraction failed: {e}")

                            except Exception as e:
                                logger.debug(f"Error analyzing element: {e}")

                        if content_found:
                            break

                    except Exception as e:
                        logger.debug(f"Selector '{selector}' failed: {e}")

                if not content_found:
                    logger.warning("No content extracted from any selector")

                    # Try alternative approaches
                    await self._try_alternative_content_extraction(data)

            else:
                logger.warning("No content selector configured")

            # Extract navigation links
            await self._extract_navigation(data, selectors)

            # Extract metadata
            data['metadata'] = await self._extract_metadata()

            return data

        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise

    async def _extract_data_list(self, url: str, target_name: str) -> Dict[str, Any]:
        """Extract chapter list from story page"""
        selectors = self.config.get_selectors(target_name)
        data = {
            'url': url,
            'timestamp': time.time(),
            'metadata': {},
            'chapters': [],
            'is_locked': False
        }

        try:
            # Extract story title first
            if 'title' in selectors:
                title_element = await self.page.query_selector(selectors['title'])
                if title_element:
                    data['title'] = await title_element.inner_text()
                    logger.info(f"Story title: {data['title']}")

            # Extract chapter links
            if 'chapter_list' in selectors and 'chapter_link' in selectors:
                # First try to find the chapter list container
                chapter_list = await self.page.query_selector(selectors['chapter_list'])
                if chapter_list:
                    # Get all chapter links within the container
                    chapter_links = await chapter_list.query_selector_all(selectors['chapter_link'])
                    logger.info(f"Found {len(chapter_links)} chapter links")

                    for i, link in enumerate(chapter_links):
                        try:
                            href = await link.get_attribute('href')
                            title = await link.inner_text()

                            if href and title:
                                # Make sure href is absolute URL
                                if href.startswith('/'):
                                    base_url = self.config.get(f'targets.{target_name}.base_url', '')
                                    href = base_url + href
                                elif not href.startswith('http'):
                                    # Relative URL, construct full URL
                                    from urllib.parse import urljoin
                                    href = urljoin(url, href)

                                chapter_data = {
                                    'url': href,
                                    'title': title.strip()
                                }
                                data['chapters'].append(chapter_data)
                                logger.info(f"📄 Chapter {i+1}: {title.strip()[:50]}... -> {href}")
                            else:
                                logger.warning(f"⚠️ Chapter link {i+1} missing href or title")

                        except Exception as e:
                            logger.warning(f"⚠️ Error processing chapter link {i+1}: {e}")
                            continue

                    logger.info(f"✅ Successfully extracted {len(data['chapters'])} chapter links")
                else:
                    logger.warning(f"❌ Chapter list container not found with selector: {selectors['chapter_list']}")

                    # Fallback: try to find chapter links directly on the page
                    logger.info("🔄 Trying fallback method to find chapter links...")
                    all_links = await self.page.query_selector_all(selectors['chapter_link'])
                    logger.info(f"🔍 Found {len(all_links)} potential chapter links on page")

                    for i, link in enumerate(all_links):
                        try:
                            href = await link.get_attribute('href')
                            title = await link.inner_text()

                            if href and title and 'chuong' in href.lower():
                                # Make sure href is absolute URL
                                if href.startswith('/'):
                                    base_url = self.config.get(f'targets.{target_name}.base_url', '')
                                    href = base_url + href
                                elif not href.startswith('http'):
                                    from urllib.parse import urljoin
                                    href = urljoin(url, href)

                                chapter_data = {
                                    'url': href,
                                    'title': title.strip()
                                }
                                data['chapters'].append(chapter_data)
                                logger.info(f"📄 Fallback Chapter {i+1}: {title.strip()[:50]}... -> {href}")

                        except Exception as e:
                            logger.warning(f"⚠️ Error processing fallback chapter link {i+1}: {e}")
                            continue

                    logger.info(f"✅ Fallback method found {len(data['chapters'])} chapter links")
            else:
                logger.warning("❌ No chapter list or chapter link selectors configured")

            # Extract metadata
            data['metadata'] = await self._extract_metadata()

            return data

        except Exception as e:
            logger.error(f"Chapter list extraction failed: {e}")
            raise

    async def _extract_formatted_content(self, element) -> str:
        """
        Extract content while preserving formatting like line breaks and paragraphs.
        Converts HTML structure to formatted text with proper spacing and line breaks after sentences.
        """
        try:
            # Get text content which already preserves line breaks from HTML structure
            text_content = await element.text_content()
            
            if not text_content:
                return ""
            
            # Use JavaScript to format content with comprehensive standardized rules
            formatted_content = await element.evaluate("""
                (element) => {
                    let text = element.textContent || '';
                    
                    // Normalize whitespace and line breaks
                    text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');
                    
                    // Remove excessive whitespace but preserve intentional line breaks
                    text = text.replace(/[ \\t]+/g, ' ');
                    
                    // COMPREHENSIVE PUNCTUATION CLEANUP
                    // Remove line breaks before ANY punctuation marks
                    text = text.replace(/\\s*\\n+\\s*([.,;:!?)])/g, '$1');
                    
                    // Remove line breaks before closing brackets, parentheses, etc.
                    text = text.replace(/\\s*\\n+\\s*([)\\]}>}])/g, '$1');
                    
                    // Remove line breaks before opening brackets when they follow punctuation
                    text = text.replace(/([.!?])\\s*\\n+\\s*([({\\[<])/g, '$1 $2');
                    
                    // Handle ellipsis patterns - remove line breaks around dots
                    text = text.replace(/\\s*\\n+\\s*(\\.+)\\s*\\n*/g, '$1');
                    text = text.replace(/(\\.+)\\s*\\n+\\s*/g, '$1 ');
                    
                    // Handle standalone punctuation on separate lines
                    text = text.replace(/\\n+\\s*\\.\\s*\\n+/g, '. \\n');
                    text = text.replace(/\\n+\\s*\\)\\s*\\n+/g, ') \\n');
                    text = text.replace(/\\n+\\s*\\(\\s*\\n+/g, ' (');
                    
                    // Clean up dialogue and thought patterns
                    // Remove line breaks within parenthetical thoughts
                    text = text.replace(/\\(\\s*\\n+/g, '(');
                    text = text.replace(/\\n+\\s*\\)/g, ')');
                    
                    // IMPROVED SENTENCE FORMATTING FOR BETTER READABILITY
                    // Add line breaks after sentence endings for better readability
                    // Handle Vietnamese punctuation and common sentence patterns
                    
                    // First, normalize existing line breaks after sentences
                    text = text.replace(/([.!?])\\s*\\n*\\s*/g, '$1 ');
                    
                    // Add line breaks after sentence endings when followed by uppercase letters
                    // Handle Vietnamese uppercase letters
                    text = text.replace(/([.!?])\\s+(?=[A-Z])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ÈÉẸẺẼÊỀẾỆỂỄ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ÌÍỊỈĨ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ÙÚỤỦŨƯỪỨỰỬỮ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[ỲÝỴỶỸ])/g, '$1\\n\\n');
                    text = text.replace(/([.!?])\\s+(?=[Đ])/g, '$1\\n\\n');
                    
                    // Add line breaks after Vietnamese dialogue markers and special punctuation
                    text = text.replace(/(】|」|』)\\s*/g, '$1\\n\\n');
                    text = text.replace(/([.!?]")\\s*/g, '$1\\n\\n');
                    text = text.replace(/([.!?]')\\s*/g, '$1\\n\\n');
                    
                    // Handle quotation marks at end of sentences using unicode escapes
                    text = text.replace(/([.!?]\\s*[\\u201C\\u201D"])\\s+(?=[A-Z])/g, '$1\\n\\n');
                    text = text.replace(/([.!?]\\s*[\\u2018\\u2019'])\\s+(?=[A-Z])/g, '$1\\n\\n');
                    
                    // Add line breaks after colon when followed by dialogue
                    text = text.replace(/(:)\\s+(?=[\\u201C\\u201D"])/g, '$1\\n\\n');
                    
                    // Ensure proper spacing around dialogue
                    text = text.replace(/([.!?])\\s*([\\u201C\\u201D"])/g, '$1$2\\n\\n');
                    
                    // Clean up multiple consecutive line breaks (max 2)
                    text = text.replace(/\\n{3,}/g, '\\n\\n');
                    
                    // Remove leading/trailing whitespace from each line
                    text = text.split('\\n').map(line => line.trim()).join('\\n');
                    
                    // Final cleanup - remove empty lines at start/end and normalize spacing
                    text = text.replace(/^\\n+|\\n+$/g, '');
                    text = text.replace(/\\n\\s*\\n/g, '\\n\\n');
                    
                    // Ensure content doesn't start or end with line breaks
                    text = text.trim();
                    
                    return text;
                }
            """)
            
            return formatted_content or text_content
            
        except Exception as e:
            logger.debug(f"Formatted content extraction failed, falling back to text content: {e}")
            
            # Fallback to simple text extraction
            try:
                return await element.text_content() or ""
            except Exception:
                try:
                    return await element.inner_text() or ""
                except Exception:
                    return ""

    async def _try_alternative_content_extraction(self, data: Dict[str, Any]) -> None:
        """Try alternative methods to extract content"""
        logger.debug("Trying alternative content extraction methods...")

        # Common selectors for Vietnamese novel sites
        alternative_selectors = [
            "#chapter-content",
            ".chapter-content",
            ".content",
            "[id*='content']",
            "[class*='content']",
            ".reading-content",
            ".chapter-text",
            ".story-content",
            "div[id*='chapter']",
            "div[class*='chapter']",
            ".break-words",  # Common class on webtruyen
            "main",
            "article"
        ]

        for selector in alternative_selectors:
            try:
                elements = await self.page.query_selector_all(selector)

                for element in elements:
                    try:
                        # Use formatted content extraction to preserve formatting
                        formatted_content = await self._extract_formatted_content(element)
                        if formatted_content and len(formatted_content.strip()) > 100:
                            # Check if it's not navigation or metadata
                            if not any(keyword in formatted_content.lower() for keyword in
                                     ['chương trước', 'chương sau', 'mục lục', 'đánh giá', 'bình luận']):
                                data['content'] = formatted_content.strip()
                                logger.debug(f"Alternative content extraction successful with: {selector}")
                                return

                    except Exception as e:
                        logger.debug(f"Error with element: {e}")

            except Exception as e:
                logger.debug(f"Alternative selector '{selector}' failed: {e}")

        # Try JavaScript execution to get content
        await self._try_javascript_content_extraction(data)

    async def _try_javascript_content_extraction(self, data: Dict[str, Any]) -> None:
        """Try JavaScript-based content extraction"""
        logger.debug("Trying JavaScript content extraction...")

        js_scripts = [
            # Try to find the main content area
            """
            (() => {
                const contentSelectors = [
                    '#chapter-content', '.chapter-content', '.content',
                    '[id*="content"]', '[class*="content"]', '.break-words'
                ];

                for (const selector of contentSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const el of elements) {
                        const text = el.textContent || el.innerText;
                        if (text && text.trim().length > 100) {
                            return {
                                selector: selector,
                                content: text.trim(),
                                length: text.trim().length
                            };
                        }
                    }
                }
                return null;
            })()
            """,

            # Try to get all text content and filter
            """
            (() => {
                const allText = document.body.textContent || document.body.innerText;
                if (allText && allText.length > 500) {
                    return {
                        selector: 'body',
                        content: allText.trim(),
                        length: allText.trim().length
                    };
                }
                return null;
            })()
            """
        ]

        for i, script in enumerate(js_scripts):
            try:
                result = await self.page.evaluate(script)

                if result and result.get('content'):
                    if not data['content']:
                        data['content'] = result['content']
                        logger.debug(f"JavaScript content extraction successful")
                        return

            except Exception as e:
                logger.debug(f"JavaScript method {i+1} failed: {e}")
    
    async def _extract_navigation(self, data: Dict[str, Any], selectors: Dict[str, str]) -> None:
        """Extract navigation links"""
        nav_data = {}

        try:
            # Next chapter link - try multiple approaches
            if 'next_chapter' in selectors:
                try:
                    next_element = await self.page.query_selector(selectors['next_chapter'])
                    if next_element:
                        next_href = await next_element.get_attribute('href')
                        if next_href:
                            nav_data['next_chapter'] = urljoin(data['url'], next_href)
                except Exception:
                    # Fallback: look for any link containing "sau" or "next"
                    next_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in next_elements:
                        text = await element.inner_text()
                        if 'sau' in text.lower() or 'next' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['next_chapter'] = urljoin(data['url'], href)
                                break

            # Previous chapter link - try multiple approaches
            if 'prev_chapter' in selectors:
                try:
                    prev_element = await self.page.query_selector(selectors['prev_chapter'])
                    if prev_element:
                        prev_href = await prev_element.get_attribute('href')
                        if prev_href:
                            nav_data['prev_chapter'] = urljoin(data['url'], prev_href)
                except Exception:
                    # Fallback: look for any link containing "trước" or "prev"
                    prev_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in prev_elements:
                        text = await element.inner_text()
                        if 'trước' in text.lower() or 'prev' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['prev_chapter'] = urljoin(data['url'], href)
                                break

        except Exception as e:
            logger.warning(f"Navigation extraction failed: {e}")

        data['navigation'] = nav_data
    
    async def _extract_metadata(self) -> Dict[str, Any]:
        """Extract page metadata"""
        metadata = {}
        
        try:
            # Page title
            title = await self.page.title()
            metadata['page_title'] = title
            
            # Meta description
            desc_element = await self.page.query_selector('meta[name="description"]')
            if desc_element:
                metadata['description'] = await desc_element.get_attribute('content')
            
            # Meta keywords
            keywords_element = await self.page.query_selector('meta[name="keywords"]')
            if keywords_element:
                metadata['keywords'] = await keywords_element.get_attribute('content')
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Metadata extraction failed: {e}")
            return metadata
