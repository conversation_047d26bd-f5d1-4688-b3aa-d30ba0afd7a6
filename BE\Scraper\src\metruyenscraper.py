"""
Main MetruyenScraper Class
Orchestrates the entire scraping process with all components
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from loguru import logger

from .config_manager import ConfigManager
from .scraper_engine import ScraperEngine
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorType, ScrapingError
from .data_processor import DataProcessor


class MetruyenScraper:
    """
    Main scraper class for Vietnamese novel websites.

    This class orchestrates all scraping components including:
    - Browser automation with anti-detection measures
    - Error handling and retry logic
    - Data processing and export
    - Configuration management

    Designed specifically for webtruyen.diendantruyen.com but can be adapted
    for other Vietnamese novel sites by modifying the configuration.

    Example:
        async with MetruyenScraper() as scraper:
            data = await scraper.scrape_url(url, "webtruyen")
            exported_files = scraper.export_data()
    """

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the scraper with configuration.

        Args:
            config_path: Path to the YAML configuration file containing
                        selectors, delays, and other scraping settings
        """
        self.config = ConfigManager(config_path)
        self.error_handler = ErrorHandler(self.config)
        self.data_processor = DataProcessor(self.config)
        self.scraper_engine: Optional[ScraperEngine] = None

        logger.info("MetruyenScraper initialized")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Start the scraper engine"""
        self.scraper_engine = ScraperEngine(self.config.config_path)
        await self.scraper_engine.start()
        logger.info("MetruyenScraper started")
    
    async def close(self) -> None:
        """Close the scraper engine"""
        if self.scraper_engine:
            await self.scraper_engine.close()
        logger.info("MetruyenScraper closed")
    
    @property
    def retry_on_error(self):
        """Expose retry decorator"""
        return self.error_handler.retry_on_error
    
    async def scrape_url(self, url: str, target_name: str = "webtruyen") -> Optional[Dict[str, Any]]:
        """
        Scrape a single URL with automatic retry and error handling.

        This method applies anti-detection measures, handles network errors,
        and automatically retries failed requests. The scraped data is
        automatically added to the internal cache for later export.

        Args:
            url: The URL to scrape (chapter or story page)
            target_name: Target website configuration to use (default: "webtruyen")

        Returns:
            Dictionary containing scraped data with keys:
            - 'title': Page/chapter title
            - 'content': Main text content
            - 'url': Original URL
            - 'timestamp': Scraping timestamp
            - 'is_locked': Whether content is behind paywall
            - 'navigation': Next/previous chapter links
            - 'metadata': Additional page metadata

        Returns None if scraping fails after all retry attempts.
        """
        if not self.scraper_engine:
            raise RuntimeError("Scraper not started. Use async context manager.")

        # Apply retry logic for network and timeout errors
        @self.retry_on_error(retry_on=[ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR])
        async def _scrape_with_retry():
            return await self.scraper_engine.scrape_url(url, target_name)

        try:
            data = await _scrape_with_retry()
            self.data_processor.add_data(data)
            return data

        except ScrapingError as e:
            logger.error(f"Failed to scrape {url}: {e}")
            return None
    
    async def scrape_urls(self, urls: List[str], target_name: str = "webtruyen", 
                         max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """Scrape multiple URLs concurrently"""
        if not urls:
            return []
        
        logger.info(f"Starting to scrape {len(urls)} URLs")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def scrape_with_semaphore(url: str) -> Optional[Dict[str, Any]]:
            async with semaphore:
                return await self.scrape_url(url, target_name)
        
        # Execute scraping tasks
        tasks = [scrape_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error scraping {urls[i]}: {result}")
            elif result is not None:
                successful_results.append(result)
        
        logger.info(f"Successfully scraped {len(successful_results)}/{len(urls)} URLs")
        return successful_results
    
    def _display_chapter_list(self, chapters: List[Dict]) -> None:
        """Display numbered list of chapters for user selection"""
        print("\n" + "="*80)
        print("📚 AVAILABLE CHAPTERS")
        print("="*80)
        
        for i, chapter in enumerate(chapters, 1):
            chapter_title = chapter.get('title', f'Chapter {i}')
            # Truncate long titles for better display
            display_title = chapter_title[:70] + "..." if len(chapter_title) > 70 else chapter_title
            print(f"{i:3d}. {display_title}")
        
        print("="*80)
        print(f"Total: {len(chapters)} chapters available")
        print("="*80 + "\n")

    def _get_user_chapter_range(self, chapters: List[Dict]) -> tuple[int, int]:
        """Get chapter range from user input with validation"""
        total_chapters = len(chapters)
        
        while True:
            try:
                print(f"📖 Select chapter range to scrape (1-{total_chapters}):")
                print("💡 Press Enter to scrape all chapters")
                
                start_input = input(f"🔢 Start chapter (1-{total_chapters}): ").strip()
                if not start_input:
                    return 1, total_chapters
                
                start_chapter = int(start_input)
                
                end_input = input(f"🔢 End chapter ({start_chapter}-{total_chapters}): ").strip()
                if not end_input:
                    end_chapter = total_chapters
                else:
                    end_chapter = int(end_input)
                
                if self._validate_chapter_range(start_chapter, end_chapter, total_chapters):
                    return start_chapter, end_chapter
                else:
                    print("❌ Invalid range! Please try again.\n")
                    
            except ValueError:
                print("❌ Please enter valid numbers!\n")
            except KeyboardInterrupt:
                print("\n🛑 Operation cancelled by user")
                raise
            except EOFError:
                print("\n🛑 Input cancelled")
                raise

    def _validate_chapter_range(self, start: int, end: int, total_chapters: int) -> bool:
        """Validate chapter range input"""
        if start < 1 or start > total_chapters:
            print(f"❌ Start chapter must be between 1 and {total_chapters}")
            return False
        
        if end < start or end > total_chapters:
            print(f"❌ End chapter must be between {start} and {total_chapters}")
            return False
        
        return True

    async def scrape_story_chapters(self, story_url: str, target_name: str = "webtruyen",
                                  max_chapters: Optional[int] = None,
                                  interactive: bool = True,
                                  start_chapter: Optional[int] = None,
                                  end_chapter: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Scrape chapters of a story with optional user interaction for chapter range selection
        
        Args:
            story_url: URL of the story's table of contents page
            target_name: Target website configuration to use (default: "webtruyen")
            max_chapters: Maximum number of chapters to scrape (applied after range selection)
            interactive: Whether to ask user for chapter range selection (default: True)
            start_chapter: Start chapter number (1-based, overrides interactive mode)
            end_chapter: End chapter number (1-based, overrides interactive mode)
            
        Returns:
            List of dictionaries containing scraped chapter data
            
        Example:
            # Interactive mode - asks user for chapter range
            chapters = await scraper.scrape_story_chapters(story_url)
            
            # Non-interactive mode - scrape specific range
            chapters = await scraper.scrape_story_chapters(
                story_url, 
                interactive=False, 
                start_chapter=26, 
                end_chapter=35
            )
        """
        logger.info(f"📚 Starting to scrape story chapters from: {story_url}")

        # First, scrape the main story page to get chapter links
        logger.info("🔍 Extracting chapter list from story page...")
        story_data = await self.scraper_engine.scrape_chapters(story_url, target_name)
        if not story_data:
            logger.error(f"❌ Failed to scrape story page: {story_url}")
            return []

        # Extract chapter information from story_data
        chapters = story_data.get('chapters', [])
        logger.info(f"📋 Found {len(chapters)} chapters in story index")

        if not chapters:
            logger.warning("❌ No chapters found in story index")
            return [story_data] if story_data else []

        # Determine chapter range to scrape
        if start_chapter is not None and end_chapter is not None:
            # Use provided range (non-interactive mode)
            if not self._validate_chapter_range(start_chapter, end_chapter, len(chapters)):
                logger.error(f"❌ Invalid chapter range: {start_chapter}-{end_chapter}")
                return []
            selected_start, selected_end = start_chapter, end_chapter
            logger.info(f"📊 Using provided chapter range: {selected_start}-{selected_end}")
            
        elif interactive:
            # Interactive mode - ask user for range
            try:
                self._display_chapter_list(chapters)
                selected_start, selected_end = self._get_user_chapter_range(chapters)
                logger.info(f"📊 User selected chapter range: {selected_start}-{selected_end}")
            except (KeyboardInterrupt, EOFError):
                logger.info("🛑 Chapter selection cancelled by user")
                return []
        else:
            # Default: scrape all chapters
            selected_start, selected_end = 1, len(chapters)
            logger.info(f"📊 Scraping all chapters: {selected_start}-{selected_end}")

        # Select chapters in the specified range (convert to 0-based indexing)
        selected_chapters = chapters[selected_start-1:selected_end]
        logger.info(f"🎯 Selected {len(selected_chapters)} chapters to scrape")

        # Extract URLs from selected chapters
        chapter_urls = []
        for i, chapter in enumerate(selected_chapters):
            if isinstance(chapter, dict):
                chapter_url = chapter.get('url')
                chapter_title = chapter.get('title', f'Chapter {selected_start + i}')
                if chapter_url:
                    chapter_urls.append(chapter_url)
                    logger.info(f"📄 {chapter_title[:50]}...")
                else:
                    logger.warning(f"⚠️ Chapter {chapter_title} missing URL: {chapter}")
            else:
                logger.warning(f"⚠️ Invalid chapter data format: {chapter}")

        # Apply max_chapters limit if specified
        if max_chapters and len(chapter_urls) > max_chapters:
            original_count = len(chapter_urls)
            chapter_urls = chapter_urls[:max_chapters]
            logger.info(f"📊 Limited to first {max_chapters} chapters (from {original_count} selected)")

        if not chapter_urls:
            logger.warning("❌ No valid chapter URLs found in selected range")
            return []

        logger.info(f"🚀 Starting to scrape {len(chapter_urls)} chapters...")

        # Scrape selected chapters with progress tracking
        chapter_results = []
        for i, chapter_url in enumerate(chapter_urls):
            try:
                current_chapter_num = selected_start + i
                logger.info(f"📖 Scraping chapter {current_chapter_num} ({i+1}/{len(chapter_urls)}): {chapter_url}")
                
                chapter_data = await self.scrape_url(chapter_url, target_name)
                if chapter_data:
                    # Add chapter number to the data for reference
                    chapter_data['chapter_number'] = current_chapter_num
                    chapter_results.append(chapter_data)
                    content_length = len(chapter_data.get('content', ''))
                    logger.info(f"✅ Chapter {current_chapter_num} scraped successfully ({content_length} chars)")
                else:
                    logger.warning(f"⚠️ Chapter {current_chapter_num} returned no data")
            except Exception as e:
                logger.error(f"❌ Failed to scrape chapter {current_chapter_num}: {e}")
                continue

        success_count = len(chapter_results)
        total_attempted = len(chapter_urls)
        logger.info(f"🎉 Successfully scraped {success_count}/{total_attempted} chapters")

        # Return only chapter results with proper formatting
        return chapter_results
    
    async def scrape_with_pagination(self, base_url: str, target_name: str = "webtruyen",
                                   max_pages: Optional[int] = None) -> List[Dict[str, Any]]:
        """Scrape content with pagination support"""
        logger.info(f"Starting pagination scraping from: {base_url}")
        
        all_results = []
        current_url = base_url
        page_count = 0
        
        while current_url and (max_pages is None or page_count < max_pages):
            page_count += 1
            logger.info(f"Scraping page {page_count}: {current_url}")
            
            page_data = await self.scrape_url(current_url, target_name)
            if not page_data:
                logger.warning(f"Failed to scrape page {page_count}")
                break
            
            all_results.append(page_data)
            
            # Look for next page URL
            navigation = page_data.get('navigation', {})
            current_url = navigation.get('next_chapter')  # or next_page depending on site
            
            if not current_url:
                logger.info("No more pages found")
                break
        
        logger.info(f"Completed pagination scraping: {len(all_results)} pages")
        return all_results
    
    def export_data(self, output_dir: str = "output") -> Dict[str, str]:
        """Export all scraped data"""
        return self.data_processor.export_data(output_dir)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics"""
        data_stats = self.data_processor.get_statistics()
        error_stats = self.error_handler.get_error_statistics()
        
        return {
            'data_statistics': data_stats,
            'error_statistics': error_stats,
            'failed_urls': self.error_handler.get_failed_urls()
        }
    
    def clear_data(self) -> None:
        """Clear all cached data and statistics"""
        self.data_processor.clear_cache()
        self.error_handler.clear_error_stats()
        logger.info("All data and statistics cleared")
    
    async def test_target_site(self, test_url: str, target_name: str = "webtruyen") -> Dict[str, Any]:
        """Test scraping capabilities on target site"""
        logger.info(f"Testing scraping on: {test_url}")
        
        test_results = {
            'url': test_url,
            'target': target_name,
            'success': False,
            'data_extracted': False,
            'content_locked': False,
            'navigation_found': False,
            'errors': []
        }
        
        try:
            data = await self.scrape_url(test_url, target_name)
            
            if data:
                test_results['success'] = True
                test_results['data_extracted'] = bool(data.get('content'))
                test_results['content_locked'] = data.get('is_locked', False)
                test_results['navigation_found'] = bool(data.get('navigation', {}).get('chapters'))
                
                logger.info("Test scraping successful")
            else:
                test_results['errors'].append("No data returned")
                logger.warning("Test scraping returned no data")
        
        except Exception as e:
            test_results['errors'].append(str(e))
            logger.error(f"Test scraping failed: {e}")
        
        return test_results
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate current configuration"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # Check required configuration sections
            required_sections = ['scraper', 'targets', 'output']
            for section in required_sections:
                if not self.config.get(section):
                    validation_results['errors'].append(f"Missing required section: {section}")
                    validation_results['valid'] = False
            
            # Check target configuration
            targets = self.config.get('targets', {})
            if not targets:
                validation_results['errors'].append("No target websites configured")
                validation_results['valid'] = False
            
            # Check browser configuration
            browser_config = self.config.get('scraper.browser', {})
            if not browser_config:
                validation_results['warnings'].append("No browser configuration found, using defaults")
            
            logger.info(f"Configuration validation: {'PASSED' if validation_results['valid'] else 'FAILED'}")
            
        except Exception as e:
            validation_results['errors'].append(f"Configuration validation error: {e}")
            validation_results['valid'] = False
        
        return validation_results
