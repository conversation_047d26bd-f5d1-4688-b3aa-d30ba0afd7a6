#!/usr/bin/env python3
"""
Final Integration Test Suite

This script performs end-to-end integration testing to verify the complete
workflow from scraping to data retrieval works correctly.
"""

import requests
import json
import time
from datetime import datetime

def test_complete_workflow():
    """Test the complete workflow from story scraping to data retrieval"""
    print("🔍 Testing Complete Workflow Integration...")
    
    # Step 1: Scrape story information
    print("   Step 1: Scraping story information...")
    story_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    story_payload = {
        "story_url": story_url,
        "include_chapters": True,
        "max_chapters": 2
    }
    
    try:
        story_response = requests.post(
            "http://localhost:8000/api/v1/scraping/story-info",
            json=story_payload,
            timeout=60
        )
        
        if story_response.status_code != 200:
            print(f"     ❌ Story scraping failed: {story_response.status_code}")
            return False
        
        story_data = story_response.json()
        if not story_data.get("success"):
            print(f"     ❌ Story scraping unsuccessful: {story_data.get('message')}")
            return False
        
        story_id = story_data.get("story_id")
        story_title = story_data.get("title")
        chapters = story_data.get("chapters", [])
        
        print(f"     ✅ Story scraped: '{story_title}' (ID: {story_id})")
        print(f"     ✅ Found {len(chapters)} chapters")
        
        if not story_id or not chapters:
            print("     ❌ Missing story ID or chapters")
            return False
        
        # Step 2: Verify story is saved in database
        print("   Step 2: Verifying story in database...")
        
        stories_response = requests.get("http://localhost:8000/api/v1/data/stories", timeout=10)
        if stories_response.status_code != 200:
            print(f"     ❌ Failed to retrieve stories: {stories_response.status_code}")
            return False
        
        stories_data = stories_response.json()
        if not stories_data.get("success"):
            print("     ❌ Stories retrieval unsuccessful")
            return False
        
        stories = stories_data.get("data", [])
        found_story = None
        for story in stories:
            if story.get("id") == story_id:
                found_story = story
                break
        
        if not found_story:
            print(f"     ❌ Story not found in database: {story_id}")
            return False
        
        print(f"     ✅ Story found in database: '{found_story.get('title')}'")
        
        # Step 3: Test individual story retrieval
        print("   Step 3: Testing individual story retrieval...")
        
        story_detail_response = requests.get(
            f"http://localhost:8000/api/v1/data/stories/{story_id}",
            timeout=10
        )
        
        if story_detail_response.status_code != 200:
            print(f"     ❌ Story detail retrieval failed: {story_detail_response.status_code}")
            return False
        
        story_detail_data = story_detail_response.json()
        if not story_detail_data.get("success"):
            print("     ❌ Story detail retrieval unsuccessful")
            return False
        
        print(f"     ✅ Story details retrieved successfully")
        
        # Step 4: Test chapter retrieval
        print("   Step 4: Testing chapter retrieval...")
        
        chapters_response = requests.get(
            f"http://localhost:8000/api/v1/data/stories/{story_id}/chapters",
            timeout=10
        )
        
        if chapters_response.status_code != 200:
            print(f"     ❌ Chapters retrieval failed: {chapters_response.status_code}")
            return False
        
        chapters_data = chapters_response.json()
        if not chapters_data.get("success"):
            print("     ❌ Chapters retrieval unsuccessful")
            return False
        
        retrieved_chapters = chapters_data.get("data", [])
        print(f"     ✅ Retrieved {len(retrieved_chapters)} chapters from database")
        
        # Step 5: Test search functionality
        print("   Step 5: Testing search functionality...")
        
        search_response = requests.get(
            f"http://localhost:8000/api/v1/data/search?q={story_title[:10]}",
            timeout=10
        )
        
        if search_response.status_code == 200:
            search_data = search_response.json()
            if search_data.get("success"):
                search_results = search_data.get("data", [])
                print(f"     ✅ Search returned {len(search_results)} results")
            else:
                print("     ⚠️  Search endpoint exists but returned no success")
        else:
            print(f"     ⚠️  Search endpoint not available: {search_response.status_code}")
        
        print("✅ Complete workflow integration test passed!")
        return True
        
    except Exception as e:
        print(f"     ❌ Workflow test failed with exception: {e}")
        return False

def test_data_consistency():
    """Test data consistency across different endpoints"""
    print("\n🔍 Testing Data Consistency...")
    
    try:
        # Get stories from main endpoint
        stories_response = requests.get("http://localhost:8000/api/v1/data/stories", timeout=10)
        if stories_response.status_code != 200:
            print("     ❌ Failed to get stories list")
            return False
        
        stories_data = stories_response.json()
        if not stories_data.get("success"):
            print("     ❌ Stories list unsuccessful")
            return False
        
        stories = stories_data.get("data", [])
        if not stories:
            print("     ⚠️  No stories in database to test consistency")
            return True
        
        # Test first story
        test_story = stories[0]
        story_id = test_story.get("id")
        
        # Get story details
        detail_response = requests.get(
            f"http://localhost:8000/api/v1/data/stories/{story_id}",
            timeout=10
        )
        
        if detail_response.status_code != 200:
            print(f"     ❌ Failed to get story details for {story_id}")
            return False
        
        detail_data = detail_response.json()
        if not detail_data.get("success"):
            print("     ❌ Story details unsuccessful")
            return False
        
        story_detail = detail_data.get("data", {})
        
        # Check consistency
        list_title = test_story.get("title")
        detail_title = story_detail.get("title")
        
        if list_title == detail_title:
            print(f"     ✅ Title consistency verified: '{list_title}'")
        else:
            print(f"     ❌ Title inconsistency: list='{list_title}' vs detail='{detail_title}'")
            return False
        
        # Check other fields
        consistent_fields = ["author", "status"]
        for field in consistent_fields:
            list_value = test_story.get(field)
            detail_value = story_detail.get(field)
            
            if list_value == detail_value:
                print(f"     ✅ {field} consistency verified")
            else:
                print(f"     ⚠️  {field} inconsistency: list='{list_value}' vs detail='{detail_value}'")
        
        print("✅ Data consistency test passed!")
        return True
        
    except Exception as e:
        print(f"     ❌ Data consistency test failed: {e}")
        return False

def test_vietnamese_content_integrity():
    """Test that Vietnamese content maintains integrity throughout the system"""
    print("\n🔍 Testing Vietnamese Content Integrity...")
    
    try:
        # Get stories and check for Vietnamese content
        stories_response = requests.get("http://localhost:8000/api/v1/data/stories", timeout=10)
        if stories_response.status_code != 200:
            print("     ❌ Failed to get stories")
            return False
        
        stories_data = stories_response.json()
        stories = stories_data.get("data", [])
        
        vietnamese_stories = []
        for story in stories:
            title = story.get("title", "")
            # Check for Vietnamese characters
            if any(ord(char) > 127 for char in title):
                vietnamese_stories.append(story)
        
        if not vietnamese_stories:
            print("     ⚠️  No Vietnamese content found to test")
            return True
        
        test_story = vietnamese_stories[0]
        story_id = test_story.get("id")
        title = test_story.get("title")
        
        print(f"     Testing Vietnamese story: '{title}'")
        
        # Get story details and verify Vietnamese content is preserved
        detail_response = requests.get(
            f"http://localhost:8000/api/v1/data/stories/{story_id}",
            timeout=10
        )
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            if detail_data.get("success"):
                detail_story = detail_data.get("data", {})
                detail_title = detail_story.get("title", "")
                
                if title == detail_title:
                    print("     ✅ Vietnamese title preserved in story details")
                else:
                    print(f"     ❌ Vietnamese title corrupted: '{title}' -> '{detail_title}'")
                    return False
        
        # Test chapters if available
        chapters_response = requests.get(
            f"http://localhost:8000/api/v1/data/stories/{story_id}/chapters",
            timeout=10
        )
        
        if chapters_response.status_code == 200:
            chapters_data = chapters_response.json()
            if chapters_data.get("success"):
                chapters = chapters_data.get("data", [])
                
                vietnamese_chapters = [
                    ch for ch in chapters 
                    if any(ord(char) > 127 for char in ch.get("title", ""))
                ]
                
                if vietnamese_chapters:
                    print(f"     ✅ Found {len(vietnamese_chapters)} chapters with Vietnamese content")
                    
                    # Test first Vietnamese chapter
                    test_chapter = vietnamese_chapters[0]
                    chapter_title = test_chapter.get("title", "")
                    print(f"     Vietnamese chapter example: '{chapter_title[:50]}...'")
                else:
                    print("     ⚠️  No Vietnamese chapter titles found")
        
        print("✅ Vietnamese content integrity test passed!")
        return True
        
    except Exception as e:
        print(f"     ❌ Vietnamese content integrity test failed: {e}")
        return False

def test_api_performance():
    """Test basic API performance metrics"""
    print("\n🔍 Testing API Performance...")
    
    try:
        # Test response times for different endpoints
        endpoints = [
            ("Health Check", "GET", "/health"),
            ("Stories List", "GET", "/api/v1/data/stories"),
            ("Database Health", "GET", "/health/database")
        ]
        
        performance_results = []
        
        for name, method, endpoint in endpoints:
            start_time = time.time()
            
            if method == "GET":
                response = requests.get(f"http://localhost:8000{endpoint}", timeout=10)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            performance_results.append({
                "name": name,
                "response_time": response_time,
                "status": response.status_code,
                "success": response.status_code == 200
            })
            
            print(f"     {name}: {response_time:.0f}ms (Status: {response.status_code})")
        
        # Check if all endpoints responded within reasonable time (< 5 seconds)
        slow_endpoints = [r for r in performance_results if r["response_time"] > 5000]
        failed_endpoints = [r for r in performance_results if not r["success"]]
        
        if slow_endpoints:
            print(f"     ⚠️  {len(slow_endpoints)} endpoints were slow (>5s)")
        
        if failed_endpoints:
            print(f"     ❌ {len(failed_endpoints)} endpoints failed")
            return False
        
        avg_response_time = sum(r["response_time"] for r in performance_results) / len(performance_results)
        print(f"     ✅ Average response time: {avg_response_time:.0f}ms")
        
        print("✅ API performance test passed!")
        return True
        
    except Exception as e:
        print(f"     ❌ API performance test failed: {e}")
        return False

def main():
    print("🚀 Final Integration Test Suite")
    print("="*60)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not running or not healthy")
            return False
        print("✅ Server is running and healthy")
    except:
        print("❌ Cannot connect to server. Make sure it's running on localhost:8000")
        return False
    
    # Run integration tests
    tests = [
        ("Complete Workflow", test_complete_workflow),
        ("Data Consistency", test_data_consistency),
        ("Vietnamese Content Integrity", test_vietnamese_content_integrity),
        ("API Performance", test_api_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Final summary
    print("\n" + "="*60)
    print("📊 FINAL INTEGRATION TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"Test completed at: {datetime.now().isoformat()}")
    
    if passed == total:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ The Vietnamese Web Novel Scraping API is working correctly")
        print("✅ Real data scraping from Vietnamese sources is functional")
        print("✅ Database integration is working properly")
        print("✅ Vietnamese content processing is intact")
        return True
    else:
        print(f"\n⚠️  {total - passed} integration tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
