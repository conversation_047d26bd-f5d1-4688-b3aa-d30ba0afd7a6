{"database_tests": {"direct_connection": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.496638", "details": "Direct MongoDB connection successful"}, "db_manager_connection": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.504644", "details": "Database manager connection successful"}, "crud_operations": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.517473", "details": "CRUD operations working"}}, "api_tests": {"root_endpoint": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.526937", "details": "Status: 200"}, "health_endpoint": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.529047", "details": "Status: 200"}, "database_health": {"status": "FAIL", "timestamp": "2025-07-09T11:16:06.531489", "details": "Status: 503"}, "api_docs": {"status": "PASS", "timestamp": "2025-07-09T11:16:06.533641", "details": "Status: 200"}, "scraping_test": {"status": "PASS", "timestamp": "2025-07-09T11:16:33.790047", "details": "Status: 200"}}, "scraping_tests": {"story_info_scraping": {"status": "FAIL", "timestamp": "2025-07-09T11:16:33.814724", "details": "Status: 502, Response: {\"success\":false,\"message\":\"Scraping service error\",\"error_code\":\"SCRAPING_ERROR\",\"details\":\"Failed to scrape story information: Failed to find document: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python313\\\\site-packages\\\\starlette\\\\middleware\\\\base.py:141> cb=[TaskGroup._spawn.<locals>.task_done() at C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python313\\\\site-packages\\\\anyio\\\\_backends\\\\_asyncio.py:794]> got Future <Future pending cb=[_chain_future.<locals>._call_check_cancel() at C:\\\\Python313\\\\Lib\\\\asyncio\\\\futures.py:384]> attached to a different loop\",\"timestamp\":\"2025-07-09T04:16:33.813439\"}"}, "single_chapter_scraping": {"status": "FAIL", "timestamp": "2025-07-09T11:16:33.821464", "details": "Status: 502"}}, "rate_limiting_tests": {"rate_limit_headers": {"status": "PASS", "timestamp": "2025-07-09T11:16:33.883112", "details": "Rate limiting headers present"}, "rate_limiting_active": {"status": "WARN", "timestamp": "2025-07-09T11:16:33.883199", "details": "No rate limiting detected in 10 requests"}}, "vietnamese_content_tests": {"character_encoding": {"status": "FAIL", "timestamp": "2025-07-09T11:16:33.826988", "details": "Status: 502"}}, "batch_processing_tests": {}, "error_handling_tests": {"invalid_url_handling": {"status": "PASS", "timestamp": "2025-07-09T11:16:33.897237", "details": "Error properly handled: 500"}, "validation_error_handling": {"status": "FAIL", "timestamp": "2025-07-09T11:16:33.899446", "details": "Status: 429"}}, "integration_tests": {}}