# Vietnamese Web Novel Scraping and Enhancement API

A comprehensive FastAPI application for scraping Vietnamese web novels, enhancing content with AI, and providing data retrieval APIs.

## Features

### 🔍 Data Scraping APIs
- **Story Information Scraper**: Extract story metadata, author, description, and chapter lists
- **Batch Chapter Content Scraper**: Concurrent scraping with rate limiting and error handling
- **Anti-detection measures**: User agent rotation, delays, and stealth configurations

### 🤖 AI Content Enhancement APIs  
- **Batch Chapter Enhancement**: Improve Vietnamese text quality using Google Gemini AI
- **Progress Tracking**: Real-time job status and progress monitoring
- **Quality Validation**: Enhancement history and improvement metrics

### 📚 Data Retrieval APIs
- **Story List API**: Paginated stories with filtering and search
- **Story Details API**: Complete story information and metadata
- **Chapter Content API**: Original and enhanced content with comparison

### 🔧 Additional Features
- **Advanced Search**: Full-text search with filters and suggestions
- **Content Comparison**: Side-by-side, diff, and statistical comparisons
- **Job Management**: Track scraping and enhancement job progress
- **Bulk Export**: Export data in JSON, CSV, TXT, and HTML formats
- **Rate Limiting**: Configurable rate limits per endpoint
- **Comprehensive Logging**: Structured logging with rotation

## Quick Start

### Prerequisites

- Python 3.8+
- MongoDB 4.4+
- Google AI API Key (for content enhancement)

### Installation

1. **Clone the repository**
```bash
cd BE/API
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Install Playwright browsers** (for scraping)
```bash
playwright install chromium
```

4. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
```env
# Database
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=webtruyen_api

# AI Enhancement
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# API Configuration
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development
DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=api.log
```

5. **Start the API server**
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Docker Setup (Optional)

```bash
# Build the image
docker build -t webtruyen-api .

# Run with docker-compose
docker-compose up -d
```

## API Documentation

Once the server is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints Overview

### Data Scraping
- `POST /api/v1/scraping/story-info` - Scrape story information
- `POST /api/v1/scraping/batch-chapters` - Batch chapter scraping
- `GET /api/v1/scraping/test-scraping` - Test scraping service

### AI Enhancement
- `POST /api/v1/enhancement/single` - Enhance single chapter
- `POST /api/v1/enhancement/batch` - Batch chapter enhancement
- `GET /api/v1/enhancement/test-enhancement` - Test AI service

### Data Retrieval
- `GET /api/v1/data/stories` - List stories with pagination
- `GET /api/v1/data/stories/{story_id}` - Get story details
- `GET /api/v1/data/stories/{story_id}/chapters` - List story chapters
- `GET /api/v1/data/stories/{story_id}/chapters/{chapter_number}` - Get chapter content

### Job Management
- `GET /api/v1/jobs/{job_id}/status` - Get job status
- `GET /api/v1/jobs/active` - List active jobs
- `POST /api/v1/jobs/{job_id}/cancel` - Cancel job
- `GET /api/v1/jobs/statistics` - Job statistics

### Search & Filter
- `POST /api/v1/search/advanced` - Advanced search
- `GET /api/v1/search/suggestions` - Search suggestions
- `POST /api/v1/search/compare-content` - Compare content

### Data Export
- `POST /api/v1/export/` - Export data
- `GET /api/v1/export/{export_id}/download` - Download export

## Usage Examples

### 1. Scrape a Story

```python
import httpx

# Scrape story information
response = httpx.post("http://localhost:8000/api/v1/scraping/story-info", json={
    "story_url": "https://webtruyen.diendantruyen.com/truyen/example-story/",
    "include_chapters": True,
    "max_chapters": 50
})

story_data = response.json()
print(f"Story: {story_data['title']}")
print(f"Total chapters: {story_data['total_chapters']}")
```

### 2. Enhance Chapter Content

```python
# Enhance a single chapter
response = httpx.post("http://localhost:8000/api/v1/enhancement/single", json={
    "chapter_id": "chapter_id_here",
    "force_re_enhance": False
})

# Batch enhance all chapters of a story
response = httpx.post("http://localhost:8000/api/v1/enhancement/batch", json={
    "story_id": "story_id_here",
    "batch_size": 1
})

job_id = response.json()["job_id"]
```

### 3. Track Job Progress

```python
# Check job status
response = httpx.get(f"http://localhost:8000/api/v1/jobs/{job_id}/status")
status = response.json()

print(f"Progress: {status['progress_percentage']}%")
print(f"Status: {status['status']}")
```

### 4. Search and Filter Stories

```python
# Search stories
response = httpx.get("http://localhost:8000/api/v1/data/stories", params={
    "search": "romance",
    "author": "author_name",
    "page": 1,
    "page_size": 20
})

stories = response.json()["data"]
```

### 5. Export Data

```python
# Export story data
response = httpx.post("http://localhost:8000/api/v1/export/", json={
    "story_ids": ["story_id_1", "story_id_2"],
    "export_format": "json",
    "include_enhanced": True,
    "include_original": False
})

export_id = response.json()["export_id"]
download_url = response.json()["download_url"]

# Download the export
download_response = httpx.get(f"http://localhost:8000{download_url}")
```

## Configuration

### Rate Limiting
Configure rate limits in environment variables:
```env
SCRAPING_RATE_LIMIT=10  # requests per minute
SCRAPING_BURST_LIMIT=5  # burst requests
MAX_CONCURRENT_SCRAPING=3
MAX_CONCURRENT_ENHANCEMENT=1
```

### AI Enhancement
```env
AI_MODEL_NAME=gemini-2.5-flash
AI_RATE_LIMIT_DELAY=0.5  # seconds between requests
AI_MAX_RETRIES=3
AI_MAX_CONTENT_LENGTH=6000
```

### Database
```env
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=webtruyen_api
```

## Testing

Run the test suite:
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
python -m pytest test_api.py -v

# Run basic tests
python test_api.py
```

## Monitoring and Logging

### Logs
- **Application logs**: `api.log`
- **Error logs**: `api_errors.log`
- **Performance logs**: `performance.log`
- **Audit logs**: `audit.log`

### Health Checks
- `GET /health` - Basic health check
- `GET /health/database` - Database connectivity check

### Metrics
The API provides built-in metrics for monitoring:
- Request/response times
- Error rates
- Job completion rates
- Database query performance

## Architecture

```
BE/API/
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration management
├── models/                # Data models
│   ├── database.py        # MongoDB models
│   └── api_models.py      # Request/response models
├── routers/               # API route handlers
│   ├── scraping.py        # Scraping endpoints
│   ├── enhancement.py     # AI enhancement endpoints
│   ├── data.py           # Data retrieval endpoints
│   ├── jobs.py           # Job management endpoints
│   ├── search.py         # Search and filter endpoints
│   └── export.py         # Data export endpoints
├── services/              # Business logic
│   ├── scraping_service.py
│   └── enhancement_service.py
├── middleware/            # Custom middleware
│   ├── logging.py
│   ├── rate_limiting.py
│   └── error_handling.py
└── utils/                # Utilities
    ├── database.py       # Database utilities
    └── logging_config.py # Logging configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is for personal use. Please respect the terms of service of the websites being scraped.

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the logs for error details
3. Test individual components using the test endpoints
