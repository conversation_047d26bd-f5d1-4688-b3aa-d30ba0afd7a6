"""
Data Scraping API Endpoints

This module provides endpoints for scraping Vietnamese web novels,
including story information and batch chapter content scraping.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Optional
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from models.api_models import (
    StoryInfoRequest, StoryInfoResponse, ChapterInfo,
    BatchChapterScrapeRequest, BatchChapterScrapeResponse, ChapterContent,
    APIResponse
)
from models.database import Story, Chapter, ScrapingJob, StoryStatus, ScrapingStatus
from utils.database import story_service, chapter_service, scraping_job_service
from services.scraping_service import ScrapingService
from middleware.error_handling import ScrapingError, DatabaseError
from utils.logging_config import LoggerMixin
from config import get_settings

router = APIRouter()
settings = get_settings()


class ScrapingController(LoggerMixin):
    """Controller for scraping operations"""
    
    def __init__(self):
        self.scraping_service = ScrapingService()
    
    async def scrape_story_info(
        self,
        request: StoryInfoRequest,
        background_tasks: BackgroundTasks
    ) -> StoryInfoResponse:
        """Scrape story information and chapter list"""
        try:
            self.log_info(f"Starting story info scraping for: {request.story_url}")
            
            # Check if story already exists
            existing_story = await story_service.find_by_url(str(request.story_url))
            
            # Scrape story information
            story_data = await self.scraping_service.scrape_story_info(
                str(request.story_url),
                include_chapters=request.include_chapters,
                max_chapters=request.max_chapters
            )
            
            # Create or update story in database
            story_id = await self._save_story_data(story_data, existing_story)
            
            # Create scraping job if chapters were included
            job_id = None
            if request.include_chapters and story_data.get("chapters"):
                job_id = await self._create_scraping_job(
                    story_id,
                    story_data["chapters"],
                    "story_info"
                )
                
                # Start background scraping if requested
                background_tasks.add_task(
                    self._background_chapter_scraping,
                    job_id,
                    story_data["chapters"]
                )
            
            # Prepare response
            chapters = [
                ChapterInfo(
                    chapter_number=i + 1,
                    title=chapter["title"],
                    url=chapter["url"]
                )
                for i, chapter in enumerate(story_data.get("chapters", []))
            ]
            
            # Get metadata properly
            metadata = story_data.get("metadata", {})

            return StoryInfoResponse(
                success=True,
                message="Story information scraped successfully",
                story_id=str(story_id),
                title=story_data["title"],
                author=metadata.get("author", "Unknown"),
                description=metadata.get("description", ""),
                cover_image_url=metadata.get("cover_image_url"),
                status=StoryStatus(metadata.get("status", "ongoing")),
                total_chapters=metadata.get("total_chapters", len(story_data.get("chapters", []))),
                chapters=chapters,
                scraping_job_id=str(job_id) if job_id else None
            )
            
        except Exception as e:
            self.log_error(f"Error scraping story info: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}")
    
    async def batch_scrape_chapters(
        self,
        request: BatchChapterScrapeRequest,
        background_tasks: BackgroundTasks
    ) -> BatchChapterScrapeResponse:
        """Scrape multiple chapters in batch"""
        try:
            self.log_info(f"Starting batch chapter scraping for {len(request.chapter_urls)} chapters")
            
            # Validate URLs
            chapter_urls = [str(url) for url in request.chapter_urls]
            
            # Get or create story
            story_id = None
            if request.story_id:
                story = await story_service.find_by_id(request.story_id)
                if story:
                    story_id = story["_id"]
            
            # Create scraping job
            job_data = {
                "story_id": story_id,
                "job_type": "batch_chapters",
                "total_items": len(chapter_urls),
                "chapter_urls": chapter_urls,
                "max_concurrent": request.max_concurrent,
                "rate_limit_delay": request.rate_limit_delay
            }
            
            job_id = await scraping_job_service.create_job(job_data)
            
            # Start background scraping
            background_tasks.add_task(
                self._background_batch_scraping,
                job_id,
                chapter_urls,
                request.max_concurrent,
                request.rate_limit_delay
            )
            
            return BatchChapterScrapeResponse(
                success=True,
                message="Batch chapter scraping started",
                job_id=str(job_id),
                total_chapters=len(chapter_urls),
                chapters=[],  # Will be populated as scraping progresses
                failed_urls=[]
            )
            
        except Exception as e:
            self.log_error(f"Error starting batch chapter scraping: {e}")
            raise ScrapingError(f"Failed to start batch scraping: {e}")
    
    async def _save_story_data(
        self,
        story_data: dict,
        existing_story: Optional[dict] = None
    ) -> str:
        """Save or update story data in database"""
        try:
            # Debug: Log the story_data structure
            logger.info(f"📝 Saving story data - Title: {story_data.get('title')}")
            logger.info(f"📝 Story data keys: {list(story_data.keys())}")
            logger.info(f"📝 Metadata keys: {list(story_data.get('metadata', {}).keys())}")

            # Ensure we have a title
            title = story_data.get("title")
            if not title:
                raise ValueError("Story title is required but not found in story_data")

            # Generate slug from title
            slug = self._generate_slug(title)

            # Extract metadata properly from story_data
            metadata = story_data.get("metadata", {})

            story_doc = {
                "title": title,
                "url": story_data["url"],
                "slug": slug,
                "scraping_status": ScrapingStatus.IN_PROGRESS,
                "author": metadata.get("author", "Unknown"),
                "description": metadata.get("description", ""),
                "cover_image_url": metadata.get("cover_image_url", ""),
                "status": metadata.get("status", "ongoing"),
                "total_chapters": story_data.get("total_chapters", len(story_data.get("chapters", []))),
                "metadata": {
                    "scraped_at": datetime.now(timezone.utc),
                    "source_website": "webtruyen",
                    "total_pages_crawled": metadata.get("total_pages_crawled", 1),
                    "all_chapter_urls": metadata.get("all_chapter_urls", []),
                    "included_chapters": metadata.get("included_chapters", 0)
                }
            }
            
            if existing_story:
                # Update existing story
                await story_service.update_by_id(existing_story["_id"], {"$set": story_doc})
                return str(existing_story["_id"])
            else:
                # Create new story
                story_id = await story_service.insert_one(story_doc)
                return str(story_id)
                
        except Exception as e:
            self.log_error(f"Error saving story data: {e}")
            raise DatabaseError(f"Failed to save story data: {e}")
    
    async def _create_scraping_job(
        self,
        story_id: str,
        chapters: List[dict],
        job_type: str
    ) -> str:
        """Create scraping job for chapters"""
        try:
            chapter_urls = [chapter["url"] for chapter in chapters]
            
            job_data = {
                "story_id": story_id,
                "job_type": job_type,
                "total_items": len(chapters),
                "chapter_urls": chapter_urls,
                "max_concurrent": settings.max_concurrent_scraping,
                "rate_limit_delay": settings.scraping_delay_min
            }
            
            job_id = await scraping_job_service.create_job(job_data)
            return str(job_id)
            
        except Exception as e:
            self.log_error(f"Error creating scraping job: {e}")
            raise DatabaseError(f"Failed to create scraping job: {e}")
    
    async def _background_chapter_scraping(
        self,
        job_id: str,
        chapters: List[dict]
    ):
        """Background task for scraping chapters"""
        try:
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=0,
                status="in_progress"
            )
            
            completed = 0
            failed = 0
            errors = []
            
            # Process chapters with concurrency control
            semaphore = asyncio.Semaphore(settings.max_concurrent_scraping)
            
            async def scrape_single_chapter(chapter_data):
                nonlocal completed, failed, errors
                
                async with semaphore:
                    try:
                        # Add delay for rate limiting
                        await asyncio.sleep(settings.scraping_delay_min)
                        
                        # Scrape chapter content
                        content_data = await self.scraping_service.scrape_chapter_content(
                            chapter_data["url"]
                        )
                        
                        # Save chapter to database
                        await self._save_chapter_data(content_data, job_id)
                        
                        completed += 1
                        self.log_info(f"Successfully scraped chapter: {chapter_data['title']}")
                        
                    except Exception as e:
                        failed += 1
                        error_msg = f"Failed to scrape {chapter_data['url']}: {e}"
                        errors.append(error_msg)
                        self.log_error(error_msg)
                    
                    # Update job progress
                    await scraping_job_service.update_job_progress(
                        job_id,
                        completed_items=completed,
                        failed_items=failed,
                        errors=errors[-10:]  # Keep only last 10 errors
                    )
            
            # Execute scraping tasks
            tasks = [scrape_single_chapter(chapter) for chapter in chapters]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Mark job as completed
            final_status = "completed" if failed == 0 else "completed_with_errors"
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status=final_status,
                errors=errors
            )
            
            self.log_info(f"Chapter scraping job {job_id} completed: {completed} success, {failed} failed")
            
        except Exception as e:
            self.log_error(f"Error in background chapter scraping: {e}")
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status="failed",
                errors=[str(e)]
            )
    
    async def _background_batch_scraping(
        self,
        job_id: str,
        chapter_urls: List[str],
        max_concurrent: int,
        rate_limit_delay: float
    ):
        """Background task for batch chapter scraping"""
        # Similar implementation to _background_chapter_scraping
        # but with different parameters and URL handling
        pass  # Implementation would be similar to above
    
    async def _save_chapter_data(self, content_data: dict, job_id: str):
        """Save chapter content data to database"""
        try:
            # Get story_id from the job
            job_data = await scraping_job_service.find_by_id(job_id)
            if not job_data:
                raise DatabaseError(f"Job not found: {job_id}")

            story_id = job_data.get("story_id")

            # Extract chapter number from URL or title
            chapter_number = self._extract_chapter_number(
                content_data.get("url", ""),
                content_data.get("title", "")
            )

            chapter_doc = {
                "story_id": story_id,  # Can be None for standalone chapters
                "chapter_number": chapter_number,
                "title": content_data["title"],
                "url": content_data["url"],
                "original_content": content_data["content"],
                "is_scraped": True,
                "is_enhanced": False,
                "metadata": {
                    "word_count": len(content_data["content"].split()) if content_data.get("content") else 0,
                    "character_count": len(content_data["content"]) if content_data.get("content") else 0,
                    "scraped_at": datetime.now(timezone.utc),
                    "scraping_duration": content_data.get("scraping_duration"),
                    "source_url": content_data["url"],
                    "is_locked": content_data.get("is_locked", False),
                    "scraping_job_id": job_id
                }
            }
            
            # Check if chapter already exists (by story_id + chapter_number or URL)
            existing_chapter = None

            # First try to find by story_id and chapter_number (more reliable)
            if story_id and chapter_number:
                existing_chapter = await chapter_service.find_one({
                    "story_id": story_id,
                    "chapter_number": chapter_number
                })

            # Fallback: find by URL
            if not existing_chapter:
                existing_chapter = await chapter_service.find_by_url(content_data["url"])

            if existing_chapter:
                # Update existing chapter
                await chapter_service.update_by_id(existing_chapter["_id"], {"$set": chapter_doc})
                self.log_info(f"Updated existing chapter: {content_data['title']}")
            else:
                # Insert new chapter with duplicate key error handling
                try:
                    await chapter_service.insert_one(chapter_doc)
                    self.log_info(f"Inserted new chapter: {content_data['title']}")
                except Exception as insert_error:
                    # Handle duplicate key error gracefully
                    if "duplicate key error" in str(insert_error).lower():
                        self.log_warning(f"Chapter already exists (duplicate key): {content_data['title']}")
                        # Try to update instead
                        try:
                            existing_chapter = await chapter_service.find_one({
                                "story_id": story_id,
                                "chapter_number": chapter_number
                            })
                            if existing_chapter:
                                await chapter_service.update_by_id(existing_chapter["_id"], {"$set": chapter_doc})
                                self.log_info(f"Updated chapter after duplicate key error: {content_data['title']}")
                        except Exception as update_error:
                            self.log_error(f"Failed to update after duplicate key error: {update_error}")
                            raise
                    else:
                        raise insert_error
                
        except Exception as e:
            self.log_error(f"Error saving chapter data: {e}")
            raise DatabaseError(f"Failed to save chapter data: {e}")
    
    def _generate_slug(self, title: str) -> str:
        """Generate URL-friendly slug from title"""
        import re
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def _extract_chapter_number(self, url: str, title: str) -> int:
        """Extract chapter number from URL or title"""
        import re
        
        # Try to extract from URL first
        url_match = re.search(r'chuong-(\d+)', url)
        if url_match:
            return int(url_match.group(1))
        
        # Try to extract from title
        title_match = re.search(r'chương\s*(\d+)', title.lower())
        if title_match:
            return int(title_match.group(1))
        
        # Default to 1 if not found
        return 1


# ============================================================================
# Router Endpoints
# ============================================================================

controller = ScrapingController()


@router.post("/story-info", response_model=StoryInfoResponse)
async def scrape_story_info(
    request: StoryInfoRequest,
    background_tasks: BackgroundTasks
):
    """
    Scrape story information and chapter list
    
    This endpoint scrapes basic story information including title, author,
    description, and optionally the complete chapter list from a story URL.
    """
    return await controller.scrape_story_info(request, background_tasks)


@router.post("/batch-chapters", response_model=BatchChapterScrapeResponse)
async def batch_scrape_chapters(
    request: BatchChapterScrapeRequest,
    background_tasks: BackgroundTasks
):
    """
    Scrape multiple chapters in batch
    
    This endpoint accepts an array of chapter URLs and scrapes their content
    concurrently with configurable rate limiting and concurrency controls.
    """
    return await controller.batch_scrape_chapters(request, background_tasks)


@router.get("/test-scraping")
async def test_scraping():
    """Test endpoint to verify scraping functionality"""
    try:
        scraping_service = ScrapingService()
        status = await scraping_service.test_connection()
        
        return APIResponse(
            success=True,
            message=f"Scraping service status: {status}"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Scraping service unavailable: {e}"
        )
