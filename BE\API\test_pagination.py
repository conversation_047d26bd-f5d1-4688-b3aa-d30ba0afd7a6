#!/usr/bin/env python3
"""
Test script for the improved pagination system
"""

import requests
import json
import time

def test_pagination():
    """Test the improved pagination system"""
    
    url = "http://localhost:8000/api/v1/scraping/story-info"
    
    payload = {
        "story_url": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("🔄 Testing improved pagination system...")
    print(f"📡 Sending request to: {url}")
    print(f"📝 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=120)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Request successful!")
            print(f"📖 Story Title: {data.get('title', 'N/A')}")
            print(f"📚 Total Chapters: {data.get('total_chapters', 'N/A')}")
            print(f"📄 Total Pages Crawled: {data.get('metadata', {}).get('total_pages_crawled', 'N/A')}")
            print(f"🔗 Chapter URLs Found: {len(data.get('chapters', []))}")
            
            # Show first few chapters
            chapters = data.get('chapters', [])
            if chapters:
                print("\n📋 First 5 chapters:")
                for i, chapter in enumerate(chapters[:5]):
                    print(f"  {i+1}. {chapter.get('title', 'N/A')} - {chapter.get('url', 'N/A')}")
                
                if len(chapters) > 5:
                    print(f"  ... and {len(chapters) - 5} more chapters")
            
            # Show metadata
            metadata = data.get('metadata', {})
            if metadata:
                print(f"\n📊 Metadata:")
                print(f"  - Description: {metadata.get('description', 'N/A')[:100]}...")
                print(f"  - Cover Image: {metadata.get('cover_image_url', 'N/A')}")
                print(f"  - Total Pages Crawled: {metadata.get('total_pages_crawled', 'N/A')}")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out after 120 seconds")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_pagination()
