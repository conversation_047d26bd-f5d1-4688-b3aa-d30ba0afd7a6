#!/usr/bin/env python3
"""
Live API Test Script

This script starts the API server and tests it with real HTTP requests
to verify that real data scraping is working correctly.
"""

import asyncio
import json
import time
import sys
import subprocess
import signal
import requests
from pathlib import Path
from datetime import datetime

# Test URLs from test.md
TEST_URLS = {
    "story_info": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/",
    "chapter_list": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/?trang=44#chapter-list",
    "chapter_content": "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-01-tran-binh-an/"
}

class LiveAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.server_process = None
        self.test_results = []
    
    def start_server(self):
        """Start the API server"""
        print("🚀 Starting API server...")
        try:
            # Start server in background
            self.server_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "main:app", 
                "--host", "0.0.0.0", "--port", "8000", "--reload"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for server to start
            print("⏳ Waiting for server to start...")
            for i in range(30):  # Wait up to 30 seconds
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=2)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except:
                    time.sleep(1)
            
            print("❌ Server failed to start within 30 seconds")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the API server"""
        if self.server_process:
            print("🛑 Stopping API server...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            print("✅ Server stopped")
    
    def test_health_endpoints(self):
        """Test health check endpoints"""
        print("\n🔍 Testing Health Endpoints...")
        
        try:
            # Test basic health
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_test("Health Check", "PASS", f"API is healthy: {data.get('message')}")
            else:
                self.log_test("Health Check", "FAIL", f"Status: {response.status_code}")
                return False
            
            # Test database health
            response = requests.get(f"{self.base_url}/health/database", timeout=10)
            if response.status_code == 200:
                self.log_test("Database Health", "PASS", "Database connection is healthy")
            else:
                self.log_test("Database Health", "FAIL", f"Status: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Health Endpoints", "FAIL", str(e))
            return False
    
    def test_scraping_service(self):
        """Test scraping service connectivity"""
        print("\n🔍 Testing Scraping Service...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v1/scraping/test-scraping", timeout=30)
            if response.status_code == 200:
                data = response.json()
                message = data.get("message", "")
                if "Real scraping service connected" in message:
                    self.log_test("Scraping Service", "PASS", "Real scraper is connected and ready")
                    return True
                else:
                    self.log_test("Scraping Service", "WARN", f"Unexpected message: {message}")
                    return False
            else:
                self.log_test("Scraping Service", "FAIL", f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Scraping Service", "FAIL", str(e))
            return False
    
    def test_story_info_scraping(self):
        """Test real story information scraping"""
        print("\n🔍 Testing Story Info Scraping...")
        
        try:
            payload = {
                "story_url": TEST_URLS["story_info"],
                "include_chapters": True,
                "max_chapters": 3
            }
            
            print(f"   Scraping: {TEST_URLS['story_info']}")
            response = requests.post(
                f"{self.base_url}/api/v1/scraping/story-info", 
                json=payload, 
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    title = data.get("title", "Unknown")
                    total_chapters = data.get("total_chapters", 0)
                    
                    # Check if it's real data
                    if "Mock" not in title and title != "Unknown":
                        self.log_test("Story Info Scraping", "PASS", 
                                    f"Successfully scraped: '{title}' with {total_chapters} chapters")
                        
                        # Test Vietnamese content
                        if any(ord(char) > 127 for char in title):
                            self.log_test("Vietnamese Content", "PASS", 
                                        "Vietnamese characters properly handled")
                        
                        return data
                    else:
                        self.log_test("Story Info Scraping", "FAIL", 
                                    f"Mock or invalid data returned: {title}")
                        return None
                else:
                    self.log_test("Story Info Scraping", "FAIL", 
                                f"Request failed: {data.get('message')}")
                    return None
            else:
                self.log_test("Story Info Scraping", "FAIL", 
                            f"HTTP {response.status_code}: {response.text[:200]}")
                return None
                
        except Exception as e:
            self.log_test("Story Info Scraping", "FAIL", str(e))
            return None
    
    def test_chapter_scraping(self):
        """Test single chapter content scraping"""
        print("\n🔍 Testing Chapter Content Scraping...")
        
        try:
            payload = {
                "chapter_urls": [TEST_URLS["chapter_content"]],
                "max_concurrent": 1,
                "rate_limit_delay": 2.0
            }
            
            print(f"   Scraping chapter: {TEST_URLS['chapter_content']}")
            response = requests.post(
                f"{self.base_url}/api/v1/scraping/batch-chapters", 
                json=payload, 
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    job_id = data.get("job_id")
                    self.log_test("Chapter Scraping Job", "PASS", f"Job created: {job_id}")
                    
                    # Wait and check job status
                    time.sleep(10)  # Wait for scraping to complete
                    
                    job_response = requests.get(f"{self.base_url}/api/v1/jobs/{job_id}", timeout=10)
                    if job_response.status_code == 200:
                        job_data = job_response.json()
                        status = job_data.get("status", "unknown")
                        completed = job_data.get("completed_items", 0)
                        
                        if status in ["completed", "completed_with_errors"] and completed > 0:
                            self.log_test("Chapter Scraping", "PASS", 
                                        f"Job completed: {completed} chapters scraped")
                            return True
                        else:
                            self.log_test("Chapter Scraping", "FAIL", 
                                        f"Job status: {status}, completed: {completed}")
                            return False
                    else:
                        self.log_test("Chapter Scraping", "FAIL", 
                                    f"Job status check failed: {job_response.status_code}")
                        return False
                else:
                    self.log_test("Chapter Scraping Job", "FAIL", 
                                f"Job creation failed: {data.get('message')}")
                    return False
            else:
                self.log_test("Chapter Scraping", "FAIL", 
                            f"HTTP {response.status_code}: {response.text[:200]}")
                return False
                
        except Exception as e:
            self.log_test("Chapter Scraping", "FAIL", str(e))
            return False
    
    def test_data_retrieval(self):
        """Test data retrieval endpoints"""
        print("\n🔍 Testing Data Retrieval...")
        
        try:
            # Test stories list
            response = requests.get(f"{self.base_url}/api/v1/data/stories", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    stories_count = len(data.get("data", []))
                    self.log_test("Stories List", "PASS", f"Retrieved {stories_count} stories")
                else:
                    self.log_test("Stories List", "FAIL", "Response indicates failure")
                    return False
            else:
                self.log_test("Stories List", "FAIL", f"Status: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("Data Retrieval", "FAIL", str(e))
            return False
    
    def test_rate_limiting(self):
        """Test rate limiting"""
        print("\n🔍 Testing Rate Limiting...")
        
        try:
            # Make multiple rapid requests
            responses = []
            start_time = time.time()
            
            for i in range(10):
                response = requests.get(f"{self.base_url}/api/v1/data/stories", timeout=5)
                responses.append(response.status_code)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Check for rate limiting
            rate_limited = any(status == 429 for status in responses)
            if rate_limited:
                self.log_test("Rate Limiting", "PASS", "Rate limiting is active (429 responses)")
            else:
                self.log_test("Rate Limiting", "WARN", 
                            f"No rate limiting detected in {len(responses)} requests over {duration:.1f}s")
            
            return True
            
        except Exception as e:
            self.log_test("Rate Limiting", "FAIL", str(e))
            return False
    
    def log_test(self, test_name, status, details=""):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"  {emoji} {test_name}: {status}")
        if details:
            print(f"     {details}")
    
    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*80)
        print("📊 LIVE API TEST REPORT")
        print("="*80)
        
        passed = sum(1 for r in self.test_results if r["status"] == "PASS")
        failed = sum(1 for r in self.test_results if r["status"] == "FAIL")
        warned = sum(1 for r in self.test_results if r["status"] == "WARN")
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Warnings: {warned}")
        
        success_rate = (passed / total * 100) if total > 0 else 0
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Save detailed report
        report_file = Path("live_test_report.json")
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return failed == 0
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Live API Testing")
        print("="*80)
        
        try:
            # Start server
            if not self.start_server():
                return False
            
            # Run tests
            tests = [
                self.test_health_endpoints,
                self.test_scraping_service,
                self.test_story_info_scraping,
                self.test_chapter_scraping,
                self.test_data_retrieval,
                self.test_rate_limiting
            ]
            
            for test_func in tests:
                try:
                    test_func()
                except Exception as e:
                    self.log_test(test_func.__name__, "FAIL", f"Test crashed: {e}")
            
            # Generate report
            success = self.generate_report()
            
            return success
            
        finally:
            self.stop_server()


def main():
    tester = LiveAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! The web scraping application is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please review the results above.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
