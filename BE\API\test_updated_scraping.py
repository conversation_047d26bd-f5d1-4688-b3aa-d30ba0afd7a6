#!/usr/bin/env python3
"""
Test Updated Scraping API

This script tests the updated scraping API with new story page selectors.
"""

import requests
import json
import time
from datetime import datetime

def test_story_info_scraping():
    """Test story info scraping with updated selectors"""
    print("🔍 Testing Updated Story Info Scraping")
    print("="*60)
    
    test_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
    
    payload = {
        "story_url": test_url,
        "include_chapters": True,
        "max_chapters": 5
    }
    
    try:
        print(f"📖 Testing story scraping: {test_url}")
        
        response = requests.post(
            "http://localhost:8000/api/v1/scraping/story-info",
            json=payload,
            timeout=120
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            if data.get("success"):
                story_id = data.get("story_id")
                title = data.get("title")
                author = data.get("author")
                description = data.get("description")
                cover_image = data.get("cover_image_url")
                total_chapters = data.get("total_chapters", 0)
                chapters = data.get("chapters", [])
                
                print(f"✅ Story scraped successfully!")
                print(f"   Story ID: {story_id}")
                print(f"   Title: {title}")
                print(f"   Author: {author}")
                print(f"   Description: {description[:100] if description else 'None'}...")
                print(f"   Cover Image: {cover_image}")
                print(f"   Total chapters: {total_chapters}")
                print(f"   Included chapters: {len(chapters)}")
                
                # Show first few chapters
                print(f"\n📄 Chapter samples:")
                for i, chapter in enumerate(chapters[:3]):
                    print(f"   Chapter {i+1}: {chapter.get('title', 'No title')}")
                    print(f"     URL: {chapter.get('url', 'No URL')}")
                
                # Test database persistence
                print(f"\n📊 Testing database persistence...")
                return test_database_retrieval(story_id, title)
                
            else:
                print(f"❌ Story scraping failed: {data.get('message')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_database_retrieval(story_id, expected_title):
    """Test database retrieval to verify data persistence"""
    try:
        # Get story from database
        response = requests.get(f"http://localhost:8000/api/v1/data/stories/{story_id}", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                story_data = data.get("data", {})
                db_title = story_data.get("title")
                db_author = story_data.get("author")
                db_description = story_data.get("description")
                db_cover_image = story_data.get("cover_image_url")
                
                print(f"✅ Story found in database")
                print(f"   Database title: {db_title}")
                print(f"   Database author: {db_author}")
                print(f"   Database description: {db_description[:100] if db_description else 'None'}...")
                print(f"   Database cover image: {db_cover_image}")
                
                # Check title consistency
                if db_title == expected_title:
                    print(f"✅ Title consistency verified")
                else:
                    print(f"⚠️  Title mismatch: expected '{expected_title}', got '{db_title}'")
                
                # Check if Vietnamese characters are preserved
                if db_title and any(ord(char) > 127 for char in db_title):
                    print(f"✅ Vietnamese characters preserved in database")
                
                return True
            else:
                print(f"❌ Database retrieval failed: {data.get('message')}")
                return False
        else:
            print(f"❌ Database request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Database retrieval test failed: {e}")
        return False

def test_multiple_stories():
    """Test scraping multiple different stories to verify selector robustness"""
    print(f"\n🔍 Testing Multiple Stories")
    print("="*60)
    
    test_urls = [
        "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/",
        # Add more URLs here if needed
    ]
    
    results = []
    
    for i, url in enumerate(test_urls):
        print(f"\n📖 Testing story {i+1}: {url}")
        
        payload = {
            "story_url": url,
            "include_chapters": False  # Just test basic info
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/scraping/story-info",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    title = data.get("title")
                    description = data.get("description")
                    
                    print(f"   ✅ Title: {title}")
                    print(f"   ✅ Description: {description[:50] if description else 'None'}...")
                    results.append(True)
                else:
                    print(f"   ❌ Failed: {data.get('message')}")
                    results.append(False)
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            results.append(False)
        
        # Add delay between requests
        if i < len(test_urls) - 1:
            time.sleep(3)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 Multiple stories test: {success_count}/{total_count} successful")
    return success_count == total_count

def test_selector_validation():
    """Test that the new selectors are being used correctly"""
    print(f"\n🔍 Testing Selector Validation")
    print("="*60)
    
    # This is a simple test to verify the scraper is using the new selectors
    # We'll check the config file to make sure our selectors are there
    
    try:
        import yaml
        
        config_path = "../Scraper/config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        selectors = config.get('targets', {}).get('webtruyen', {}).get('selectors', {})
        
        required_selectors = [
            'story_title',
            'story_image', 
            'story_description',
            'story_chapters',
            'story_pagination'
        ]
        
        print("📋 Checking selector configuration:")
        all_present = True
        
        for selector in required_selectors:
            if selector in selectors:
                print(f"   ✅ {selector}: {selectors[selector]}")
            else:
                print(f"   ❌ {selector}: Missing")
                all_present = False
        
        if all_present:
            print("✅ All required selectors are configured")
            return True
        else:
            print("❌ Some selectors are missing")
            return False
            
    except Exception as e:
        print(f"❌ Selector validation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Updated Scraping API")
    print("="*60)
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not healthy")
            return False
        print("✅ Server is healthy")
    except:
        print("❌ Cannot connect to server")
        return False
    
    # Run tests
    tests = [
        ("Selector Validation", test_selector_validation),
        ("Story Info Scraping", test_story_info_scraping),
        ("Multiple Stories", test_multiple_stories)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 FINAL TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"Test completed at: {datetime.now().isoformat()}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Updated scraping API is working correctly")
        print("✅ New story page selectors are functional")
        print("✅ Vietnamese content processing is intact")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
