2025-07-09 10:55:20 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: api.log
2025-07-09 10:55:20 | INFO     | logging:callHandlers:1744 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 10:55:20 | ERROR    | logging:callHandlers:1744 | ❌ Failed to connect to database: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-09 10:55:47 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: api.log
2025-07-09 10:55:47 | INFO     | logging:callHandlers:1744 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 10:55:47 | INFO     | logging:callHandlers:1744 | ✅ Database connection established
2025-07-09 10:55:47 | INFO     | logging:callHandlers:1744 | ✅ Application startup completed
2025-07-09 10:56:01 | INFO     | logging:callHandlers:1744 | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-09 10:56:01 | INFO     | logging:callHandlers:1744 | ✅ Application shutdown completed
2025-07-09 10:56:03 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: api.log
2025-07-09 10:56:03 | INFO     | logging:callHandlers:1744 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 10:56:03 | INFO     | logging:callHandlers:1744 | ✅ Database connection established
2025-07-09 10:56:03 | INFO     | logging:callHandlers:1744 | ✅ Application startup completed
2025-07-09 10:56:16 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: api.log
2025-07-09 10:56:16 | INFO     | logging:callHandlers:1744 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 10:56:16 | INFO     | logging:callHandlers:1744 | ✅ Database connection established
2025-07-09 10:56:16 | INFO     | logging:callHandlers:1744 | ✅ Application startup completed
2025-07-09 10:56:41 | INFO     | logging:callHandlers:1744 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/8.13.0...
2025-07-09 10:56:41 | INFO     | logging:callHandlers:1744 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 10:56:44 | INFO     | logging:callHandlers:1744 | 🔵 GET / - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:56:44 | INFO     | logging:callHandlers:1744 | ✅ GET / - Status: 200 - Time: 0.004s - Size: 187
2025-07-09 10:56:44 | INFO     | logging:callHandlers:1744 | 🔵 GET /favicon.ico - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:56:44 | INFO     | logging:callHandlers:1744 | ⚠️ GET /favicon.ico - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 10:56:49 | INFO     | logging:callHandlers:1744 | 🔵 GET / - Client: 127.0.0.1 - User-Agent: curl/8.13.0...
2025-07-09 10:56:49 | INFO     | logging:callHandlers:1744 | ✅ GET / - Status: 200 - Time: 0.001s - Size: 187
2025-07-09 10:56:55 | INFO     | logging:callHandlers:1744 | 🔵 GET /api/v1/data/stories - Client: 127.0.0.1 - User-Agent: curl/8.13.0...
2025-07-09 10:56:55 | INFO     | logging:callHandlers:1744 | Getting story list - page: 1, size: 20
2025-07-09 10:56:55 | INFO     | logging:callHandlers:1744 | ✅ GET /api/v1/data/stories - Status: 200 - Time: 0.015s - Size: 208
2025-07-09 10:56:58 | INFO     | logging:callHandlers:1744 | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:56:58 | INFO     | logging:callHandlers:1744 | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 948
2025-07-09 10:56:59 | INFO     | logging:callHandlers:1744 | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:56:59 | INFO     | logging:callHandlers:1744 | ✅ GET /openapi.json - Status: 200 - Time: 0.023s - Size: 38154
2025-07-09 10:57:02 | INFO     | logging:callHandlers:1744 | 🔵 GET /api/v1/scraping/test-scraping - Client: 127.0.0.1 - User-Agent: curl/8.13.0...
2025-07-09 10:57:02 | INFO     | logging:callHandlers:1744 | Initializing scraping service (mock)...
2025-07-09 10:57:02 | INFO     | logging:callHandlers:1744 | ✅ Scraping service initialized successfully (mock)
2025-07-09 10:57:02 | INFO     | logging:callHandlers:1744 | ✅ GET /api/v1/scraping/test-scraping - Status: 200 - Time: 0.002s - Size: 136
2025-07-09 10:57:08 | INFO     | logging:callHandlers:1744 | 🔵 GET /api/v1/enhancement/test-enhancement - Client: 127.0.0.1 - User-Agent: curl/8.13.0...
2025-07-09 10:57:08 | INFO     | logging:callHandlers:1744 | Initializing AI enhancement service (mock)...
2025-07-09 10:57:08 | INFO     | logging:callHandlers:1744 | ✅ AI enhancement service initialized successfully (mock)
2025-07-09 10:57:08 | INFO     | logging:callHandlers:1744 | ✅ GET /api/v1/enhancement/test-enhancement - Status: 200 - Time: 0.001s - Size: 147
2025-07-09 10:57:14 | INFO     | logging:callHandlers:1744 | 🔵 GET /docs - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:57:14 | INFO     | logging:callHandlers:1744 | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 948
2025-07-09 10:57:14 | INFO     | logging:callHandlers:1744 | 🔵 GET /openapi.json - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:57:14 | INFO     | logging:callHandlers:1744 | ✅ GET /openapi.json - Status: 200 - Time: 0.002s - Size: 38154
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | 🔵 POST /api/v1/scraping/story-info - Client: 127.0.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Starting story info scraping for: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Initializing scraping service (mock)...
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | ✅ Scraping service initialized successfully (mock)
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Mock scraping story info from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Successfully mock scraped story info: Mock Story Title (10 chapters)
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Inserted document with ID: 686de8c8278cd2981f0e224a
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Inserted document with ID: 686de8c8278cd2981f0e224b
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | ✅ POST /api/v1/scraping/story-info - Status: 200 - Time: 0.013s - Size: 2058
2025-07-09 10:58:00 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-1
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-2
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-3
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Inserted document with ID: 686de8c9278cd2981f0e224e
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Successfully scraped chapter: Chapter 3: Mock Chapter Title
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:01 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9D490>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9D490>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-1', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-1: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9D490>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9D490>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BA66160>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1E50>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-1', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F650>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F650>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-2', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-2: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F650>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F650>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16160>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCF1F40>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-2', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:01 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-4
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | ✅ Scraping service cleaned up (mock)
2025-07-09 10:58:02 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F140>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F140>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-4', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-4: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F140>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F140>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BD16700>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BDA5A90>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-4', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-5
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-6
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:02 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBF1D0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBF1D0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-5', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-5: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBF1D0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBF1D0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B420>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A7B0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-5', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9FB60>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9FB60>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-6', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-6: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9FB60>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9FB60>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1F80>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90A72A350>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-6', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:02 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-7
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:03 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBFAD0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBFAD0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-7', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-7: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBFAD0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BBBFAD0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BB6B4C0>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BC7BAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-7', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-8
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-9
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:03 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F2F0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F2F0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-8', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-8: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F2F0>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8CFC0>) at 2100436627728
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9F2F0>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1620>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBCB0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-8', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EE70>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EE70>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-9', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-9: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EE70>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EE70>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD1B20>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCDBAF0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-9', 'title': 'Mock ...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:03 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:04 | INFO     | logging:callHandlers:1744 | Mock scraping chapter content from: https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-10
2025-07-09 10:58:04 | INFO     | logging:callHandlers:1744 | Successfully mock scraped chapter: Mock Chapter Title (83 words)
2025-07-09 10:58:04 | WARNING  | logging:callHandlers:1744 | Duplicate key error: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:04 | ERROR    | logging:callHandlers:1744 | Error saving chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EF90>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EF90>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-10', 'title': 'Mock...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:04 | ERROR    | logging:callHandlers:1744 | Failed to scrape https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-10: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 100, in insert_one
    result = await self.collection.insert_one(document)
                   │    │                     └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
                   │    └ <property object at 0x000001E909096C00>
                   └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "C:\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             │        │            └ None
             │        └ None
             └ None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 891, in insert_one
    self._insert_one(
    │    └ <function Collection._insert_one at 0x000001E908EAAF20>
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 831, in _insert_one
    self._database.client._retryable_write(
    │    │         └ <property object at 0x000001E908EC2F70>
    │    └ Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Mo...
    └ Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverIn...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2061, in _retryable_write
    return self._retry_with_session(retryable, func, s, bulk, operation, operation_id)
           │    │                   │          │     │  │     │          └ None
           │    │                   │          │     │  │     └ <_Op.INSERT: 'insert'>
           │    │                   │          │     │  └ None
           │    │                   │          │     └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EF90>
           │    │                   │          └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>
           │    │                   └ True
           │    └ <function MongoClient._retry_with_session at 0x000001E908F1F600>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1947, in _retry_with_session
    return self._retry_internal(
           │    └ <function MongoClient._retry_internal at 0x000001E908F1F740>
           └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\_csot.py", line 125, in csot_wrapper
    return func(self, *args, **kwargs)
           │    │      │       └ {'func': <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>, 'session': <pymongo.synchronous.cl...
           │    │      └ ()
           │    └ MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=False, driver=DriverInfo(name='Motor', ver...
           └ <function MongoClient._retry_internal at 0x000001E908F1F6A0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           │    │          │    │             │    └ <function _ClientConnectionRetryable._write at 0x000001E908F20D60>
           │    │          │    │             └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    │          │    └ False
           │    │          └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    └ <function _ClientConnectionRetryable._read at 0x000001E908F20E00>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\mongo_client.py", line 2862, in _write
    return self._func(self._session, conn, self._retryable)  # type: ignore
           │    │     │    │         │     │    └ False
           │    │     │    │         │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    │     │    │         └ Connection(<pymongo.network_layer.NetworkingInterface object at 0x000001E90BC8E9E0>) at 2100436629968
           │    │     │    └ <pymongo.synchronous.client_session.ClientSession object at 0x000001E90BC9EF90>
           │    │     └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
           │    └ <function Collection._insert_one.<locals>._insert_command at 0x000001E90BCD2D40>
           └ <pymongo.synchronous.mongo_client._ClientConnectionRetryable object at 0x000001E90BCE90F0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\synchronous\collection.py", line 829, in _insert_command
    _check_write_command_response(result)
    │                             └ {'n': 0, 'writeErrors': [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters...
    └ <function _check_write_command_response at 0x000001E907630FE0>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 288, in _check_write_command_response
    _raise_last_write_error(write_errors)
    │                       └ [{'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapt...
    └ <function _raise_last_write_error at 0x000001E907630E00>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pymongo\helpers_shared.py", line 258, in _raise_last_write_error
    raise DuplicateKeyError(error.get("errmsg"), 11000, error)
          │                 │     │                     └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          │                 │     └ <method 'get' of 'dict' objects>
          │                 └ {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapte...
          └ <class 'pymongo.errors.DuplicateKeyError'>

pymongo.errors.DuplicateKeyError: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 347, in _save_chapter_data
    await chapter_service.insert_one(chapter_doc)
          │               │          └ {'story_id': None, 'chapter_number': 1, 'title': 'Mock Chapter Title', 'url': 'https://webtruyen.diendantruyen.com/truyen/vo-...
          │               └ <function DatabaseService.insert_one at 0x000001E909340F40>
          └ <utils.database.ChapterService object at 0x000001E9090CACF0>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\utils\database.py", line 106, in insert_one
    raise DatabaseError(f"Document already exists: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x000001E904385D00>
  File "C:\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x000001E904295620>
           └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E904294B80>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E90B8D2CF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
    │    └ <function subprocess_started at 0x000001E90B931EE0>
    └ <SpawnProcess name='SpawnProcess-1' parent=12972 started>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E90B8D2E40>>
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=884, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E90B930FE0>
           │       │   └ <uvicorn.server.Server object at 0x000001E90B8D2E40>
           │       └ <function run at 0x000001E9049A40E0>
           └ <module 'asyncio' from 'C:\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E90B929D20>
           │      └ <function Runner.run at 0x000001E90632C720>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packag...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E906332200>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E90B8D38C0>
  File "C:\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E906332160>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E906333F60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E9049A56C0>
    └ <Handle Task.task_wakeup()>
  File "C:\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>

> File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 256, in scrape_single_chapter
    await self._save_chapter_data(content_data, job_id)
          │    │                  │             └ '686de8c8278cd2981f0e224b'
          │    │                  └ {'url': 'https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem//chapter-10', 'title': 'Mock...
          │    └ <function ScrapingController._save_chapter_data at 0x000001E90A76A5C0>
          └ <routers.scraping.ScrapingController object at 0x000001E90A6C3B60>

  File "D:\Personal Projects\Vibe\Webtruyen\BE\API\routers\scraping.py", line 351, in _save_chapter_data
    raise DatabaseError(f"Failed to save chapter data: {e}")
          └ <class 'middleware.error_handling.DatabaseError'>

middleware.error_handling.DatabaseError: Failed to save chapter data: Document already exists: E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: webtruyen_api.chapters index: story_id_1_chapter_number_1 dup key: { story_id: null, chapter_number: 1 }', 'keyPattern': {'story_id': 1, 'chapter_number': 1}, 'keyValue': {'story_id': None, 'chapter_number': 1}}
2025-07-09 10:58:04 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:04 | INFO     | logging:callHandlers:1744 | Updated document matching filter: {'_id': ObjectId('686de8c8278cd2981f0e224b')}
2025-07-09 10:58:04 | INFO     | logging:callHandlers:1744 | Chapter scraping job 686de8c8278cd2981f0e224b completed: 1 success, 9 failed
