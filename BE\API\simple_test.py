#!/usr/bin/env python3
"""
Simple Test Script for Vietnamese Web Novel API

This script performs basic testing to verify the API is working.
"""

import sys
import json
import time
from pathlib import Path

# Test basic imports first
try:
    import fastapi
    import uvicorn
    import motor
    import pymongo
    print("✅ Core dependencies imported successfully")
except ImportError as e:
    print(f"❌ Failed to import core dependencies: {e}")
    sys.exit(1)

# Test configuration
try:
    from config import get_settings
    settings = get_settings()
    print(f"✅ Configuration loaded: {settings.app_name}")
except Exception as e:
    print(f"❌ Failed to load configuration: {e}")
    sys.exit(1)

# Test database connection
def test_database():
    print("\n🔍 Testing Database Connection...")
    try:
        client = pymongo.MongoClient(settings.mongodb_url, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        client.close()
        print("✅ MongoDB connection successful")
        return True
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

# Test API import
def test_api_import():
    print("\n🔍 Testing API Import...")
    try:
        from main import app
        print("✅ FastAPI app imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import FastAPI app: {e}")
        return False

# Test basic endpoints with TestClient
def test_basic_endpoints():
    print("\n🔍 Testing Basic Endpoints...")
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint: {data.get('name', 'Unknown')}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health endpoint: {data.get('message', 'Unknown')}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")
        return False

# Test scraping service
def test_scraping_service():
    print("\n🔍 Testing Scraping Service...")
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test scraping test endpoint
        response = client.get("/api/v1/scraping/test-scraping")
        if response.status_code in [200, 503]:  # 503 is acceptable if not configured
            print(f"✅ Scraping test endpoint accessible: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Message: {data.get('message', 'No message')}")
        else:
            print(f"❌ Scraping test endpoint failed: {response.status_code}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Scraping service test failed: {e}")
        return False

# Test with real URLs
def test_real_scraping():
    print("\n🔍 Testing Real Scraping...")
    try:
        import asyncio
        from fastapi.testclient import TestClient
        from main import app
        from models.database import db_manager

        # Initialize database connection for testing
        async def init_db():
            try:
                await db_manager.connect(settings.mongodb_url)
                print("   Database connected for testing")
                return True
            except Exception as e:
                print(f"   Database connection failed: {e}")
                return False

        # Run database initialization
        db_connected = asyncio.run(init_db())
        if not db_connected:
            print("❌ Cannot test scraping without database")
            return False

        try:
            client = TestClient(app)

            # Test story info scraping with real URL
            test_url = "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/"
            payload = {
                "story_url": test_url,
                "include_chapters": False
            }

            print(f"   Testing with URL: {test_url}")
            response = client.post("/api/v1/scraping/story-info", json=payload)

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    title = data.get("title", "Unknown")
                    print(f"✅ Story scraping successful: {title}")

                    # Check if it's real data (not mock)
                    if "Mock" not in title:
                        print("✅ Real data scraped (not mock)")
                    else:
                        print("⚠️  Mock data returned - real scraper may not be integrated")

                    return True
                else:
                    print(f"❌ Story scraping failed: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ Story scraping request failed: {response.status_code}")
                if response.status_code != 500:  # Don't show error details for 500
                    print(f"   Response: {response.text[:200]}...")

            return False
        finally:
            # Cleanup database connection
            try:
                asyncio.run(db_manager.disconnect())
            except:
                pass

    except Exception as e:
        print(f"❌ Real scraping test failed: {e}")
        return False

def main():
    print("🚀 Simple Test Suite for Vietnamese Web Novel API")
    print("="*60)
    
    tests = [
        ("Database Connection", test_database),
        ("API Import", test_api_import),
        ("Basic Endpoints", test_basic_endpoints),
        ("Scraping Service", test_scraping_service),
        ("Real Scraping", test_real_scraping)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
