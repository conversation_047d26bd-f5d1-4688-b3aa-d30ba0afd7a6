#!/usr/bin/env python3
"""
Comprehensive Test Suite for Vietnamese Web Novel Scraping API

This script performs thorough testing of all API functionality including:
- Database connectivity
- Real data scraping from Vietnamese web novel sources
- API endpoints testing
- Rate limiting and concurrency
- Vietnamese content processing
- Batch processing
- Error handling and edge cases
- Integration tests

Usage:
    python comprehensive_test.py
"""

import asyncio
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

import httpx
import pymongo
from fastapi.testclient import TestClient

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from main import app
from config import get_settings
from models.database import db_manager


class ComprehensiveTestSuite:
    """Comprehensive test suite for the web scraping API"""
    
    def __init__(self):
        self.settings = get_settings()
        self.test_urls = {
            "story_info": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/",
            "chapter_list": "https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem/?trang=44#chapter-list",
            "chapter_content": "https://webtruyen.diendantruyen.com/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem-chuong-01-tran-binh-an/"
        }
        self.client = TestClient(app)
        self.results = {
            "database_tests": {},
            "api_tests": {},
            "scraping_tests": {},
            "rate_limiting_tests": {},
            "vietnamese_content_tests": {},
            "batch_processing_tests": {},
            "error_handling_tests": {},
            "integration_tests": {}
        }
        
    def log_test(self, category: str, test_name: str, status: str, details: str = ""):
        """Log test results"""
        timestamp = datetime.now().isoformat()
        self.results[category][test_name] = {
            "status": status,
            "timestamp": timestamp,
            "details": details
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} [{category}] {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    async def test_database_connectivity(self):
        """Test MongoDB connection and basic operations"""
        print("\n🔍 Testing Database Connectivity...")
        
        try:
            # Test direct MongoDB connection
            client = pymongo.MongoClient(self.settings.mongodb_url, serverSelectionTimeoutMS=5000)
            client.admin.command('ping')
            client.close()
            self.log_test("database_tests", "direct_connection", "PASS", "Direct MongoDB connection successful")
        except Exception as e:
            self.log_test("database_tests", "direct_connection", "FAIL", str(e))
        
        try:
            # Test database manager connection
            await db_manager.connect(self.settings.mongodb_url)
            await db_manager.client.admin.command('ping')
            self.log_test("database_tests", "db_manager_connection", "PASS", "Database manager connection successful")
        except Exception as e:
            self.log_test("database_tests", "db_manager_connection", "FAIL", str(e))
        
        try:
            # Test database operations
            test_collection = db_manager.database["test_collection"]
            test_doc = {"test": "data", "timestamp": datetime.utcnow()}
            result = await test_collection.insert_one(test_doc)
            
            # Verify insertion
            found_doc = await test_collection.find_one({"_id": result.inserted_id})
            if found_doc:
                self.log_test("database_tests", "crud_operations", "PASS", "CRUD operations working")
                # Cleanup
                await test_collection.delete_one({"_id": result.inserted_id})
            else:
                self.log_test("database_tests", "crud_operations", "FAIL", "Document not found after insertion")
        except Exception as e:
            self.log_test("database_tests", "crud_operations", "FAIL", str(e))
    
    def test_basic_api_endpoints(self):
        """Test all basic API endpoints"""
        print("\n🔍 Testing Basic API Endpoints...")
        
        # Test root endpoint
        try:
            response = self.client.get("/")
            if response.status_code == 200:
                data = response.json()
                if data.get("name") == "Vietnamese Web Novel API":
                    self.log_test("api_tests", "root_endpoint", "PASS", f"Status: {response.status_code}")
                else:
                    self.log_test("api_tests", "root_endpoint", "FAIL", f"Unexpected response: {data}")
            else:
                self.log_test("api_tests", "root_endpoint", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("api_tests", "root_endpoint", "FAIL", str(e))
        
        # Test health endpoint
        try:
            response = self.client.get("/health")
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test("api_tests", "health_endpoint", "PASS", f"Status: {response.status_code}")
                else:
                    self.log_test("api_tests", "health_endpoint", "FAIL", f"Health check failed: {data}")
            else:
                self.log_test("api_tests", "health_endpoint", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("api_tests", "health_endpoint", "FAIL", str(e))
        
        # Test database health endpoint
        try:
            response = self.client.get("/health/database")
            if response.status_code == 200:
                self.log_test("api_tests", "database_health", "PASS", f"Status: {response.status_code}")
            else:
                self.log_test("api_tests", "database_health", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("api_tests", "database_health", "FAIL", str(e))
        
        # Test API documentation
        try:
            response = self.client.get("/docs")
            if response.status_code in [200, 307]:  # 307 for redirect
                self.log_test("api_tests", "api_docs", "PASS", f"Status: {response.status_code}")
            else:
                self.log_test("api_tests", "api_docs", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("api_tests", "api_docs", "FAIL", str(e))
        
        # Test scraping test endpoint
        try:
            response = self.client.get("/api/v1/scraping/test-scraping")
            if response.status_code in [200, 503]:  # 503 acceptable if service not configured
                self.log_test("api_tests", "scraping_test", "PASS", f"Status: {response.status_code}")
            else:
                self.log_test("api_tests", "scraping_test", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("api_tests", "scraping_test", "FAIL", str(e))
    
    def test_real_data_scraping(self):
        """Test actual data scraping from Vietnamese web novel sources"""
        print("\n🔍 Testing Real Data Scraping...")
        
        # Test story info scraping
        try:
            payload = {
                "story_url": self.test_urls["story_info"],
                "include_chapters": True,
                "max_chapters": 5
            }
            response = self.client.post("/api/v1/scraping/story-info", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("title") and data.get("title") != "Mock Story Title":
                    self.log_test("scraping_tests", "story_info_scraping", "PASS", 
                                f"Scraped: {data.get('title')} with {data.get('total_chapters', 0)} chapters")
                else:
                    self.log_test("scraping_tests", "story_info_scraping", "FAIL", 
                                f"Mock data returned or missing title: {data}")
            else:
                self.log_test("scraping_tests", "story_info_scraping", "FAIL", 
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("scraping_tests", "story_info_scraping", "FAIL", str(e))
        
        # Test single chapter scraping
        try:
            payload = {
                "chapter_urls": [self.test_urls["chapter_content"]],
                "max_concurrent": 1,
                "rate_limit_delay": 2.0
            }
            response = self.client.post("/api/v1/scraping/batch-chapters", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    job_id = data.get("job_id")
                    self.log_test("scraping_tests", "single_chapter_scraping", "PASS", 
                                f"Job started: {job_id}")
                    
                    # Wait a bit and check job status
                    time.sleep(5)
                    job_response = self.client.get(f"/api/v1/jobs/{job_id}")
                    if job_response.status_code == 200:
                        job_data = job_response.json()
                        self.log_test("scraping_tests", "chapter_job_tracking", "PASS", 
                                    f"Job status: {job_data.get('status', 'unknown')}")
                    else:
                        self.log_test("scraping_tests", "chapter_job_tracking", "FAIL", 
                                    f"Job status check failed: {job_response.status_code}")
                else:
                    self.log_test("scraping_tests", "single_chapter_scraping", "FAIL", 
                                f"Job creation failed: {data}")
            else:
                self.log_test("scraping_tests", "single_chapter_scraping", "FAIL", 
                            f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("scraping_tests", "single_chapter_scraping", "FAIL", str(e))
    
    def test_vietnamese_content_processing(self):
        """Test Vietnamese language content processing"""
        print("\n🔍 Testing Vietnamese Content Processing...")
        
        # This will be tested as part of the scraping tests
        # We'll check if Vietnamese characters are properly handled
        try:
            # Test with Vietnamese URL and content
            payload = {
                "story_url": self.test_urls["story_info"],
                "include_chapters": False
            }
            response = self.client.post("/api/v1/scraping/story-info", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                title = data.get("title", "")
                
                # Check if Vietnamese characters are preserved
                vietnamese_chars = any(ord(char) > 127 for char in title)
                if vietnamese_chars or "Mock" not in title:
                    self.log_test("vietnamese_content_tests", "character_encoding", "PASS", 
                                f"Vietnamese characters preserved in title: {title[:50]}...")
                else:
                    self.log_test("vietnamese_content_tests", "character_encoding", "FAIL", 
                                f"No Vietnamese characters or mock data: {title}")
            else:
                self.log_test("vietnamese_content_tests", "character_encoding", "FAIL", 
                            f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("vietnamese_content_tests", "character_encoding", "FAIL", str(e))
    
    def test_rate_limiting(self):
        """Test rate limiting mechanisms"""
        print("\n🔍 Testing Rate Limiting...")
        
        try:
            # Make multiple rapid requests to test rate limiting
            responses = []
            start_time = time.time()
            
            for i in range(10):
                response = self.client.get("/api/v1/data/stories")
                responses.append(response.status_code)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Check for rate limiting headers
            last_response = self.client.get("/api/v1/data/stories")
            has_rate_limit_headers = (
                "X-RateLimit-Limit" in last_response.headers or
                "X-RateLimit-Remaining" in last_response.headers
            )
            
            if has_rate_limit_headers:
                self.log_test("rate_limiting_tests", "rate_limit_headers", "PASS", 
                            f"Rate limiting headers present")
            else:
                self.log_test("rate_limiting_tests", "rate_limit_headers", "WARN", 
                            f"Rate limiting headers not found")
            
            # Check if any requests were rate limited (429 status)
            rate_limited = any(status == 429 for status in responses)
            if rate_limited:
                self.log_test("rate_limiting_tests", "rate_limiting_active", "PASS", 
                            f"Rate limiting is active (429 responses)")
            else:
                self.log_test("rate_limiting_tests", "rate_limiting_active", "WARN", 
                            f"No rate limiting detected in {len(responses)} requests")
                
        except Exception as e:
            self.log_test("rate_limiting_tests", "rate_limiting_test", "FAIL", str(e))
    
    def test_error_handling(self):
        """Test error handling for various edge cases"""
        print("\n🔍 Testing Error Handling...")
        
        # Test invalid URL
        try:
            payload = {
                "story_url": "https://invalid-url-that-does-not-exist.com/story",
                "include_chapters": False
            }
            response = self.client.post("/api/v1/scraping/story-info", json=payload)
            
            if response.status_code in [400, 404, 500]:
                data = response.json()
                if not data.get("success", True):  # Should be False for error
                    self.log_test("error_handling_tests", "invalid_url_handling", "PASS", 
                                f"Error properly handled: {response.status_code}")
                else:
                    self.log_test("error_handling_tests", "invalid_url_handling", "FAIL", 
                                f"Error not properly indicated in response")
            else:
                self.log_test("error_handling_tests", "invalid_url_handling", "FAIL", 
                            f"Unexpected status: {response.status_code}")
        except Exception as e:
            self.log_test("error_handling_tests", "invalid_url_handling", "FAIL", str(e))
        
        # Test malformed request
        try:
            response = self.client.post("/api/v1/scraping/story-info", json={"invalid": "data"})
            
            if response.status_code == 422:  # Validation error
                self.log_test("error_handling_tests", "validation_error_handling", "PASS", 
                            f"Validation error properly handled")
            else:
                self.log_test("error_handling_tests", "validation_error_handling", "FAIL", 
                            f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("error_handling_tests", "validation_error_handling", "FAIL", str(e))
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("="*80)
        
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        warned_tests = 0
        
        for category, tests in self.results.items():
            if tests:
                print(f"\n📁 {category.replace('_', ' ').title()}:")
                for test_name, result in tests.items():
                    status = result["status"]
                    details = result["details"]
                    
                    total_tests += 1
                    if status == "PASS":
                        passed_tests += 1
                        emoji = "✅"
                    elif status == "FAIL":
                        failed_tests += 1
                        emoji = "❌"
                    else:
                        warned_tests += 1
                        emoji = "⚠️"
                    
                    print(f"  {emoji} {test_name}: {status}")
                    if details:
                        print(f"     {details}")
        
        print("\n" + "="*80)
        print("📈 SUMMARY")
        print("="*80)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️  Warnings: {warned_tests}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"Success Rate: {success_rate:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 All critical tests passed!")
        else:
            print(f"\n⚠️  {failed_tests} tests failed - review required")
        
        # Save detailed report to file
        report_file = Path("test_report.json")
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return {
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "warned": warned_tests,
            "success_rate": success_rate
        }


async def main():
    """Run comprehensive test suite"""
    print("🚀 Starting Comprehensive Test Suite for Vietnamese Web Novel API")
    print("="*80)
    
    test_suite = ComprehensiveTestSuite()
    
    try:
        # Run all test categories
        await test_suite.test_database_connectivity()
        test_suite.test_basic_api_endpoints()
        test_suite.test_real_data_scraping()
        test_suite.test_vietnamese_content_processing()
        test_suite.test_rate_limiting()
        test_suite.test_error_handling()
        
        # Generate final report
        summary = test_suite.generate_report()
        
        return summary["failed"] == 0
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        return False
    finally:
        # Cleanup
        try:
            await db_manager.disconnect()
        except:
            pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
