"""
Models Package

This package contains data models for the API application.
"""

from models.database import (
    Story, Chapter, ScrapingJob, EnhancementJob,
    StoryStatus, ScrapingStatus, EnhancementStatus,
    db_manager, DatabaseManager
)
from models.api_models import (
    APIResponse, PaginationInfo, PaginatedResponse,
    StoryInfoRequest, StoryInfoResponse,
    BatchChapterScrapeRequest, BatchChapterScrapeResponse,
    BatchEnhancementRequest, BatchEnhancementResponse,
    StoryListRequest, StoryListResponse,
    ChapterContentResponse, JobStatusResponse
)

__all__ = [
    # Database models
    "Story", "Chapter", "ScrapingJob", "EnhancementJob",
    "StoryStatus", "ScrapingStatus", "EnhancementStatus", 
    "db_manager", "DatabaseManager",
    
    # API models
    "APIResponse", "PaginationInfo", "PaginatedResponse",
    "StoryInfoRequest", "StoryInfoResponse",
    "BatchChapterScrapeRequest", "BatchChapterScrapeResponse", 
    "BatchEnhancementRequest", "BatchEnhancementResponse",
    "StoryListRequest", "StoryListResponse",
    "ChapterContentResponse", "JobStatusResponse"
]
